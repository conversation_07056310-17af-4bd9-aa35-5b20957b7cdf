// models/Tutor.js
const mongoose = require('mongoose');

const tutorSchema = new mongoose.Schema({
    name: String,
    subjects: [String],
    costRange: String, // e.g., __P__, __P____P__, etc.
    badges: [String],
    imagePath: String,
    description: String,
    postcodes: [String], // e.g., "Online" or Highland postcodes
    contact: String, // NEW: a simple field for email/website address
});

// ===================================================================
// DATABASE INDEXES FOR OPTIMAL SEARCH PERFORMANCE
// ===================================================================

// NOTE: MongoDB doesn't allow compound indexes on multiple array fields
// (subjects and postcodes are both arrays), so we use separate strategies

// 1. Individual indexes for array fields (subjects and postcodes)
tutorSchema.index({ subjects: 1 }); // For subject-based searches
tutorSchema.index({ postcodes: 1 }); // For location-based searches

// 2. Compound indexes with cost (non-array field)
tutorSchema.index({ costRange: 1, subjects: 1 }); // Cost + subject searches
tutorSchema.index({ costRange: 1, postcodes: 1 }); // Cost + location searches

// 3. Text index for full-text search across subjects and description
// This enables better search functionality and fuzzy matching
tutorSchema.index(
    {
        subjects: 'text',
        description: 'text',
        name: 'text',
    },
    {
        weights: {
            subjects: 10, // Subjects are most important for matching
            name: 5, // Name is moderately important
            description: 1, // Description is least important but still searchable
        },
        name: 'tutor_text_search',
    }
);

// 4. Cost-based sorting index
tutorSchema.index({ costRange: 1 }); // For cost-based sorting

// 5. Sparse index for contact field (only indexes documents that have this field)
tutorSchema.index({ contact: 1 }, { sparse: true });

module.exports = mongoose.models.Tutor || mongoose.model('Tutor', tutorSchema);
