# Dependencies
node_modules/
package-lock.json
bun.lock

# Build outputs
coverage/
playwright-report/
test-results/

# IDE and system files
.vs/
obj/
bin/
*.njsproj
*.njsproj.user
*.sln
bash.exe.stackdump

# Minified files
*.min.js
*.min.css

# Generated files
public/cache-buster.js

# Logs
*.log
npm-debug.log*

# Environment files
.env
.env.local
.env.production

# Temporary files
*.tmp
*.temp

# Documentation that should preserve formatting
CHANGELOG.md
LICENSE

# Template files
public/js/dynamic-sections-template.html
