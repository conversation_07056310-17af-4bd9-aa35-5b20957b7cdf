#!/usr/bin/env node

/**
 * Check which HTML files in public/ include visual-editor-v2.js
 * and identify any that might be missing it
 */

const fs = require('fs');
const path = require('path');

const publicDir = path.join(process.cwd(), 'public');

// Files that should have visual editor (static content pages)
const SHOULD_HAVE_VE = [
    'index.html',
    'about-us.html',
    'contact.html',
    'parents.html',
    'partnerships.html',
    'tutorMembership.html',
    'tutorszone.html',
    'tutorDirectory.html',
    'publicConnect.html',
    'tutorConnect.html',
    'page.html',
];

// Files that don't need visual editor (admin/login pages, templates)
const SHOULD_NOT_HAVE_VE = [
    'admin.html',
    'blogWriter.html',
    'login.html',
    'page-template.html',
    'demo-context-indicators.html',
    'test-visual-editor-persistence.html',
];

function checkFile(filename) {
    const filepath = path.join(publicDir, filename);

    if (!fs.existsSync(filepath)) {
        return { exists: false };
    }

    const content = fs.readFileSync(filepath, 'utf8');
    const hasVE = content.includes('visual-editor-v2.js');
    const hasTemplate = content.includes('ve-editor-modal-template');

    return {
        exists: true,
        hasVE,
        hasTemplate,
        content,
    };
}

console.log('🔍 Checking Visual Editor Script Inclusion...\n');

console.log('✅ Files that SHOULD have visual-editor-v2.js:');
const missingVE = [];
const missingTemplate = [];

SHOULD_HAVE_VE.forEach(filename => {
    const result = checkFile(filename);

    if (!result.exists) {
        console.log(`   ❌ ${filename} - FILE NOT FOUND`);
        return;
    }

    if (result.hasVE && result.hasTemplate) {
        console.log(`   ✅ ${filename} - Has VE script and template`);
    } else if (result.hasVE && !result.hasTemplate) {
        console.log(`   ⚠️  ${filename} - Has VE script but MISSING template`);
        missingTemplate.push(filename);
    } else if (!result.hasVE && result.hasTemplate) {
        console.log(`   ⚠️  ${filename} - Has template but MISSING VE script`);
        missingVE.push(filename);
    } else {
        console.log(`   ❌ ${filename} - MISSING both VE script and template`);
        missingVE.push(filename);
        missingTemplate.push(filename);
    }
});

console.log('\n🚫 Files that should NOT have visual-editor-v2.js:');
SHOULD_NOT_HAVE_VE.forEach(filename => {
    const result = checkFile(filename);

    if (!result.exists) {
        console.log(`   ⚪ ${filename} - FILE NOT FOUND`);
        return;
    }

    if (result.hasVE) {
        console.log(`   ⚠️  ${filename} - Unexpectedly has VE script`);
    } else {
        console.log(`   ✅ ${filename} - Correctly has no VE script`);
    }
});

// Summary
console.log('\n📊 SUMMARY:');
if (missingVE.length === 0 && missingTemplate.length === 0) {
    console.log('✅ All files have correct visual editor setup!');
} else {
    if (missingVE.length > 0) {
        console.log(`❌ Files missing VE script: ${missingVE.join(', ')}`);
    }
    if (missingTemplate.length > 0) {
        console.log(`❌ Files missing VE template: ${missingTemplate.join(', ')}`);
    }
}

console.log('\n🎯 EDIT BUTTON TROUBLESHOOTING:');
console.log('If edit button is not appearing:');
console.log('1. Ensure you are logged in as admin via admin.html');
console.log('2. Check browser console for admin authentication errors');
console.log('3. Verify visual-editor-v2.js is loading without errors');
console.log('4. Check that ve-editor-modal-template exists in the page');
console.log('5. Look for JavaScript errors that might prevent initialization');
