#!/usr/bin/env node

/**
 * Database Performance Testing Script
 *
 * This script tests the performance of common database queries to verify
 * that the indexes are working correctly and providing performance benefits.
 *
 * Usage: node scripts/test-index-performance.js
 * Or: npm run db:test-performance
 */

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Import models
const Tutor = require('../models/tutor');
const Blog = require('../models/Blog');
const Section = require('../models/Section');

// Use the same connection logic as the API
const connectToDatabase = require('../api/connectToDatabase');

async function testTutorQueries() {
    console.log('🎓 Testing Tutor query performance...');

    // Test 1: Subject-based search (should use subjects index)
    console.time('Tutor subject search');
    const mathTutors = await Tutor.find({
        subjects: { $regex: 'math', $options: 'i' },
    });
    console.timeEnd('Tutor subject search');
    console.log(`  Found ${mathTutors.length} math tutors`);

    // Test 2: Compound search (cost + subject)
    console.time('Tutor compound search');
    const expensiveMathTutors = await Tutor.find({
        costRange: '__P____P____P__',
        subjects: { $regex: 'math', $options: 'i' },
    });
    console.timeEnd('Tutor compound search');
    console.log(`  Found ${expensiveMathTutors.length} tutors matching compound criteria`);

    // Test 3: Text search
    console.time('Tutor text search');
    const textSearchResults = await Tutor.find({
        $text: { $search: 'mathematics experienced' },
    });
    console.timeEnd('Tutor text search');
    console.log(`  Found ${textSearchResults.length} tutors via text search`);

    // Test 4: Rolling banner query (cost sorting)
    console.time('Rolling banner query');
    const bannerTutors = await Tutor.find({}).sort({ costRange: 1 }).limit(6);
    console.timeEnd('Rolling banner query');
    console.log(`  Retrieved ${bannerTutors.length} tutors for banner`);
}

async function testBlogQueries() {
    console.log('\n📝 Testing Blog query performance...');

    // Test 1: Category filtering with date sorting
    console.time('Blog category filter');
    const parentBlogs = await Blog.find({
        status: 'published',
        category: 'parent',
    }).sort({ publishDate: -1 });
    console.timeEnd('Blog category filter');
    console.log(`  Found ${parentBlogs.length} parent category blogs`);

    // Test 2: Featured content query
    console.time('Featured blog query');
    const featuredBlogs = await Blog.find({
        status: 'published',
        featured: true,
    }).sort({ publishDate: -1 });
    console.timeEnd('Featured blog query');
    console.log(`  Found ${featuredBlogs.length} featured blogs`);

    // Test 3: Text search
    console.time('Blog text search');
    const blogSearchResults = await Blog.find({
        $text: { $search: 'education learning' },
    });
    console.timeEnd('Blog text search');
    console.log(`  Found ${blogSearchResults.length} blogs via text search`);

    // Test 4: Slug lookup
    console.time('Blog slug lookup');
    const blogBySlug = await Blog.findOne({ slug: 'test-blog-post' });
    console.timeEnd('Blog slug lookup');
    console.log(`  Slug lookup result: ${blogBySlug ? 'found' : 'not found'}`);
}

async function testSectionQueries() {
    console.log('\n📄 Testing Section query performance...');

    // Test 1: Page sections query (most common)
    console.time('Page sections query');
    const indexSections = await Section.find({
        page: 'index',
        isPublished: true,
    }).sort({ order: 1 });
    console.timeEnd('Page sections query');
    console.log(`  Found ${indexSections.length} sections for index page`);

    // Test 2: Navigation sections
    console.time('Navigation sections query');
    const navSections = await Section.find({
        showInNav: true,
        navCategory: 'about',
    });
    console.timeEnd('Navigation sections query');
    console.log(`  Found ${navSections.length} navigation sections`);

    // Test 3: Content overrides
    console.time('Content overrides query');
    const overrides = await Section.find({
        isContentOverride: true,
        targetPage: 'index',
        isActive: true,
    });
    console.timeEnd('Content overrides query');
    console.log(`  Found ${overrides.length} active content overrides`);
}

async function analyzeQueryPlans() {
    console.log('\n📊 Analyzing query execution plans...\n');

    // Analyze tutor search query
    const tutorPlan = await Tutor.find({
        subjects: { $regex: 'math', $options: 'i' },
    }).explain('executionStats');

    console.log('🎓 Tutor Search Query Analysis:');
    console.log(`  Documents examined: ${tutorPlan.executionStats.totalDocsExamined}`);
    console.log(`  Documents returned: ${tutorPlan.executionStats.totalDocsReturned}`);
    console.log(`  Execution time: ${tutorPlan.executionStats.executionTimeMillis}ms`);

    // Extract index name from the winning plan (handle different plan structures)
    let tutorIndexUsed = 'No index';
    if (tutorPlan.executionStats.winningPlan && tutorPlan.executionStats.winningPlan.inputStage) {
        tutorIndexUsed =
            tutorPlan.executionStats.winningPlan.inputStage.indexName ||
            tutorPlan.executionStats.winningPlan.inputStage.stage ||
            'Collection scan';
    } else if (tutorPlan.executionStats.winningPlan) {
        tutorIndexUsed = tutorPlan.executionStats.winningPlan.stage || 'Collection scan';
    }
    console.log(`  Index used: ${tutorIndexUsed}`);

    // Analyze blog category query
    const blogPlan = await Blog.find({
        status: 'published',
        category: 'parent',
    }).explain('executionStats');

    console.log('\n📝 Blog Category Query Analysis:');
    console.log(`  Documents examined: ${blogPlan.executionStats.totalDocsExamined}`);
    console.log(`  Documents returned: ${blogPlan.executionStats.totalDocsReturned}`);
    console.log(`  Execution time: ${blogPlan.executionStats.executionTimeMillis}ms`);

    // Extract index name from the winning plan
    let blogIndexUsed = 'No index';
    if (blogPlan.executionStats.winningPlan && blogPlan.executionStats.winningPlan.inputStage) {
        blogIndexUsed =
            blogPlan.executionStats.winningPlan.inputStage.indexName ||
            blogPlan.executionStats.winningPlan.inputStage.stage ||
            'Collection scan';
    } else if (blogPlan.executionStats.winningPlan) {
        blogIndexUsed = blogPlan.executionStats.winningPlan.stage || 'Collection scan';
    }
    console.log(`  Index used: ${blogIndexUsed}`);
}

async function main() {
    console.log('\n🔬 Testing database query performance...\n');

    try {
        await connectToDatabase();
        await testTutorQueries();
        await testBlogQueries();
        await testSectionQueries();
        await analyzeQueryPlans();

        console.log('\n🎉 Performance testing completed!\n');
        console.log('💡 Tips for interpreting results:');
        console.log('  • Lower execution times indicate better performance');
        console.log('  • "Index used" should show specific index names (not "No index")');
        console.log('  • Documents examined should be close to documents returned');
        console.log('  • Run this script before and after applying indexes to compare');
    } catch (error) {
        console.error('❌ Performance testing failed:', error);
        process.exit(1);
    } finally {
        console.log('\n🔌 Database connection closed');
        await mongoose.connection.close();
    }
}

// Run the performance tests
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, testTutorQueries, testBlogQueries, testSectionQueries };
