#!/usr/bin/env node
/**
 * <PERSON><PERSON>t to automatically fix common unused variable issues by prefixing with underscore
 * This helps maintain code while satisfying ESLint rules
 */

const fs = require('fs');
const path = require('path');

// Files and their unused variables to fix
const fixes = [
    {
        file: 'api/addTutor.js',
        fixes: [
            { line: 5, from: 'cookieParser', to: '_cookieParser' },
            { line: 142, from: 'editId', to: '_editId' },
        ],
    },
    {
        file: 'api/blog.js',
        fixes: [
            { line: 77, from: 'styleClass', to: '_styleClass' },
            { line: 81, from: 'boxClass', to: '_boxClass' },
        ],
    },
    {
        file: 'api/connectToDatabase.js',
        fixes: [{ line: 10, from: 'error', to: '_error' }],
    },
    {
        file: 'api/content-manager.js',
        fixes: [
            { line: 26, from: 'page', to: '_page' },
            { line: 26, from: 'selector', to: '_selector' },
            { line: 26, from: 'type', to: '_type' },
            { line: 26, from: 'id', to: '_id' },
            { line: 325, from: 'page', to: '_page' },
            { line: 325, from: 'selector', to: '_selector' },
        ],
    },
    {
        file: 'api/protected.js',
        fixes: [{ line: 6, from: 'res', to: '_res' }],
    },
    {
        file: 'index/config.js',
        fixes: [{ line: 4, from: 'error', to: '_error' }],
    },
    {
        file: 'scripts/migrate-indexes.js',
        fixes: [
            { line: 17, from: 'Tutor', to: '_Tutor' },
            { line: 18, from: 'Blog', to: '_Blog' },
            { line: 19, from: 'User', to: '_User' },
            { line: 20, from: 'Section', to: '_Section' },
            { line: 21, from: 'Order', to: '_Order' },
        ],
    },
    {
        file: 'seedDatabase.js',
        fixes: [{ line: 52, from: 'getRandomPostcodes', to: '_getRandomPostcodes' }],
    },
];

console.log('🔧 Fixing unused variable issues...');

fixes.forEach(({ file, fixes: fileFixes }) => {
    const filePath = path.join(process.cwd(), file);

    if (!fs.existsSync(filePath)) {
        console.log(`⚠️  File not found: ${file}`);
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    fileFixes.forEach(({ from, to }) => {
        // Replace variable declarations
        const patterns = [
            new RegExp(`\\b${from}\\b(?=\\s*=)`, 'g'), // Variable assignments
            new RegExp(`\\b${from}\\b(?=\\s*,)`, 'g'), // In destructuring
            new RegExp(`\\b${from}\\b(?=\\s*\\))`, 'g'), // Function parameters
            new RegExp(`catch\\s*\\(\\s*${from}\\s*\\)`, 'g'), // Catch blocks
        ];

        patterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, match => match.replace(from, to));
                modified = true;
            }
        });
    });

    if (modified) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ Fixed unused variables in: ${file}`);
    } else {
        console.log(`ℹ️  No changes needed in: ${file}`);
    }
});

console.log('🎉 Unused variable fixes completed!');
