// models/Blog.js
const mongoose = require('mongoose');

const blogSchema = new mongoose.Schema({
    title: String,
    author: String,
    content: String,
    imagePath: String,
    excerpt: String,
    publishDate: Date,
    category: [
        {
            type: String,
            enum: ['tutor', 'parent'],
        },
    ],
    // SEO and metadata fields
    metaDescription: {
        type: String,
        maxlength: 160, // Google's recommended limit
        default: '',
    },
    slug: {
        type: String,
        unique: true,
        sparse: true, // allows null values but ensures uniqueness when present
        default: null,
    },
    tags: {
        type: [String], // Array of tags for categorization
        default: [],
    },
    featured: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        enum: ['draft', 'published', 'archived'],
        default: 'published',
    },
    // Social media metadata
    socialImage: {
        type: String,
        default: '',
    }, // Custom image for social sharing
    socialTitle: {
        type: String,
        default: '',
    }, // Custom title for social sharing
    socialDescription: {
        type: String,
        default: '',
    }, // Custom description for social sharing
    // SEO fields
    focusKeyword: {
        type: String,
        default: '',
    }, // Primary keyword for SEO
    readingTime: {
        type: Number,
        default: 1,
    }, // Estimated reading time in minutes
    createdAt: {
        type: Date,
        default: Date.now,
    },
    updatedAt: {
        type: Date,
        default: Date.now,
    },
});

// ===================================================================
// DATABASE INDEXES FOR OPTIMAL BLOG PERFORMANCE
// ===================================================================

// 1. Compound index for category filtering with date sorting (most common query pattern)
blogSchema.index({ status: 1, category: 1, publishDate: -1 });

// 2. Featured content index for homepage queries
blogSchema.index({ status: 1, featured: -1, publishDate: -1 });

// 3. Text search index for blog content search functionality
blogSchema.index(
    {
        title: 'text',
        content: 'text',
        excerpt: 'text',
        tags: 'text',
    },
    {
        weights: {
            title: 10, // Title matches are most important
            tags: 8, // Tags are highly relevant
            excerpt: 5, // Excerpt is moderately important
            content: 1, // Content is searchable but lower priority
        },
        name: 'blog_content_search',
    }
);

// 4. Individual indexes for common queries
// Note: slug index is automatically created due to unique: true in schema
blogSchema.index({ publishDate: -1 }); // For chronological sorting
blogSchema.index({ status: 1 }); // For filtering by publication status
blogSchema.index({ category: 1 }); // For category-based filtering
blogSchema.index({ tags: 1 }); // For tag-based filtering
blogSchema.index({ author: 1, publishDate: -1 }); // For author-based queries with date sorting
blogSchema.index({ focusKeyword: 1 }, { sparse: true }); // For SEO keyword queries

module.exports = mongoose.models.Blog || mongoose.model('Blog', blogSchema);
