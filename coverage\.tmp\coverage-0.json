{"result": [{"scriptId": "420", "url": "file:///C:/Users/<USER>/source/repos/tutorScotland/tests/config/setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12163, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12163, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 547, "endOffset": 607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 698, "endOffset": 802, "count": 12}], "isBlockCoverage": true}, {"functionName": "createTestUser", "ranges": [{"startOffset": 870, "endOffset": 1180, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateJWT", "ranges": [{"startOffset": 1200, "endOffset": 1334, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestTutor", "ranges": [{"startOffset": 1358, "endOffset": 1791, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestBlog", "ranges": [{"startOffset": 1814, "endOffset": 2179, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestSection", "ranges": [{"startOffset": 2205, "endOffset": 2488, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "422", "url": "file:///C:/Users/<USER>/source/repos/tutorScotland/tests/config/test-db.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14568, "count": 1}], "isBlockCoverage": true}, {"functionName": "setupTestDB", "ranges": [{"startOffset": 429, "endOffset": 1038, "count": 1}, {"startOffset": 717, "endOffset": 790, "count": 0}, {"startOffset": 930, "endOffset": 1036, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1138, "endOffset": 1165, "count": 1}], "isBlockCoverage": true}, {"functionName": "teardownTestDB", "ranges": [{"startOffset": 1170, "endOffset": 1621, "count": 1}, {"startOffset": 1510, "endOffset": 1619, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1724, "endOffset": 1754, "count": 1}], "isBlockCoverage": true}, {"functionName": "clearTestDB", "ranges": [{"startOffset": 1759, "endOffset": 2375, "count": 12}, {"startOffset": 1871, "endOffset": 1937, "count": 0}, {"startOffset": 2267, "endOffset": 2373, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2088, "endOffset": 2139, "count": 12}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2475, "endOffset": 2502, "count": 12}], "isBlockCoverage": true}, {"functionName": "seedTestData", "ranges": [{"startOffset": 2546, "endOffset": 3240, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3341, "endOffset": 3369, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1133", "url": "file:///C:/Users/<USER>/source/repos/tutorScotland/tests/config/mocks.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8920, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8920, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 268, "endOffset": 717, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 737, "endOffset": 1251, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1276, "endOffset": 1469, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1490, "endOffset": 1748, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1765, "endOffset": 2020, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2045, "endOffset": 2461, "count": 0}], "isBlockCoverage": false}, {"functionName": "suppressConsole", "ranges": [{"startOffset": 2750, "endOffset": 2858, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2962, "endOffset": 2993, "count": 0}], "isBlockCoverage": false}, {"functionName": "restoreConsole", "ranges": [{"startOffset": 2998, "endOffset": 3144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3247, "endOffset": 3277, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1134", "url": "file:///C:/Users/<USER>/source/repos/tutorScotland/tests/unit/models/User.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 668, "endOffset": 8596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 711, "endOffset": 825, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 883, "endOffset": 4365, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1749, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1847, "endOffset": 2361, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2423, "endOffset": 2714, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2789, "endOffset": 3363, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3432, "endOffset": 3753, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3825, "endOffset": 4359, "count": 1}, {"startOffset": 3952, "endOffset": 4353, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4422, "endOffset": 5243, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4496, "endOffset": 5237, "count": 1}, {"startOffset": 4717, "endOffset": 5231, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5303, "endOffset": 7188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5388, "endOffset": 5978, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6045, "endOffset": 6655, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6710, "endOffset": 7182, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7246, "endOffset": 8592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7324, "endOffset": 7843, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7912, "endOffset": 8586, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1135", "url": "file:///C:/Users/<USER>/source/repos/tutorScotland/models/user.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2687, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2687, "count": 1}], "isBlockCoverage": true}]}]}