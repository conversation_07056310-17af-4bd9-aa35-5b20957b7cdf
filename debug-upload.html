<!DOCTYPE html>
<html>
<head>
    <title>Debug Image Upload</title>
</head>
<body>
    <h1>Debug Image Upload</h1>
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" id="fileInput" accept="image/*" required>
        <button type="submit">Upload</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = 'Please select a file';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            resultDiv.innerHTML = 'Uploading...';
            
            try {
                const response = await fetch('/api/upload-image', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<h3>Success!</h3><pre>${result}</pre>`;
                } else {
                    resultDiv.innerHTML = `<h3>Error (${response.status})</h3><pre>${result}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h3>Network Error</h3><pre>${error.message}</pre>`;
            }
        });
    </script>
</body>
</html>
