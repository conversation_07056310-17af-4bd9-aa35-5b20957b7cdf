#!/usr/bin/env node

/**
 * Database Index Migration Script
 *
 * This script applies all the database indexes defined in the Mongoose models
 * to the actual MongoDB database. It's designed to be safe to run multiple times.
 *
 * Usage: node scripts/migrate-indexes.js
 * Or: npm run db:migrate-indexes
 */

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Import models to ensure indexes are defined
const _Tutor = require('../models/tutor');
const _Blog = require('../models/Blog');
const _User = require('../models/user');
const _Section = require('../models/Section');
const _Order = require('../models/Order');

// Use the same connection logic as the API
const connectToDatabase = require('../api/connectToDatabase');

// Helper function to safely create index (skip if exists)
async function safeCreateIndex(collection, indexSpec, options = {}) {
    try {
        await collection.createIndex(indexSpec, options);
        return true;
    } catch (error) {
        if (error.code === 85) {
            // IndexOptionsConflict
            console.log(
                `    ⚠️  Index ${JSON.stringify(indexSpec)} already exists with different options`
            );
            return false;
        }
        throw error; // Re-throw other errors
    }
}

async function applyIndexes() {
    console.log('📊 Applying indexes...\n');

    // Get native MongoDB collections
    const collections = {
        tutors: mongoose.connection.collection('tutors'),
        blogs: mongoose.connection.collection('blogs'),
        users: mongoose.connection.collection('users'),
        sections: mongoose.connection.collection('sections'),
        orders: mongoose.connection.collection('orders'),
    };

    try {
        // ===================================================================
        // TUTOR INDEXES
        // ===================================================================
        console.log('🎓 Applying Tutor indexes...');

        // Individual indexes for array fields (MongoDB limitation: no compound indexes on multiple arrays)
        await safeCreateIndex(collections.tutors, { subjects: 1 });
        await safeCreateIndex(collections.tutors, { postcodes: 1 });
        console.log('  ✅ Created individual array field indexes');

        // Compound indexes with cost (non-array field)
        await safeCreateIndex(
            collections.tutors,
            { costRange: 1, subjects: 1 },
            { name: 'tutor_cost_subject' }
        );
        await safeCreateIndex(
            collections.tutors,
            { costRange: 1, postcodes: 1 },
            { name: 'tutor_cost_location' }
        );
        console.log('  ✅ Created cost-based compound indexes');

        // Text search index
        await safeCreateIndex(
            collections.tutors,
            { subjects: 'text', description: 'text', name: 'text' },
            {
                weights: { subjects: 10, name: 5, description: 1 },
                name: 'tutor_text_search',
            }
        );
        console.log('  ✅ Created text search index');

        // Cost sorting and contact indexes
        await safeCreateIndex(collections.tutors, { costRange: 1 });
        await safeCreateIndex(collections.tutors, { contact: 1 }, { sparse: true });
        console.log('  ✅ Created cost sorting and contact indexes');

        // ===================================================================
        // BLOG INDEXES
        // ===================================================================
        console.log('\n📝 Applying Blog indexes...');

        // Compound index for category filtering with date sorting
        await safeCreateIndex(collections.blogs, { status: 1, category: 1, publishDate: -1 });
        console.log('  ✅ Created category-date compound index');

        // Featured content index
        await safeCreateIndex(collections.blogs, { status: 1, featured: -1, publishDate: -1 });
        console.log('  ✅ Created featured content index');

        // Text search index
        await safeCreateIndex(
            collections.blogs,
            { title: 'text', content: 'text', excerpt: 'text', tags: 'text' },
            {
                weights: { title: 10, tags: 8, excerpt: 5, content: 1 },
                name: 'blog_content_search',
            }
        );
        console.log('  ✅ Created blog text search index');

        // Individual indexes
        await safeCreateIndex(collections.blogs, { publishDate: -1 });
        await safeCreateIndex(collections.blogs, { status: 1 });
        await safeCreateIndex(collections.blogs, { category: 1 });
        await safeCreateIndex(collections.blogs, { tags: 1 });
        await safeCreateIndex(collections.blogs, { author: 1, publishDate: -1 });
        await safeCreateIndex(collections.blogs, { focusKeyword: 1 }, { sparse: true });
        console.log('  ✅ Created individual blog indexes');

        // ===================================================================
        // USER INDEXES
        // ===================================================================
        console.log('\n👤 Applying User indexes...');

        // Role-based indexes
        await safeCreateIndex(collections.users, { role: 1 });
        await safeCreateIndex(collections.users, { role: 1, email: 1 });
        console.log('  ✅ Created user authentication indexes');

        // ===================================================================
        // SECTION INDEXES
        // ===================================================================
        console.log('\n📄 Applying Section indexes...');

        // Page-order compound index
        await safeCreateIndex(collections.sections, { page: 1, order: 1, isPublished: 1 });
        console.log('  ✅ Created page-order compound index');

        // Full page index
        await safeCreateIndex(collections.sections, { isFullPage: 1, isPublished: 1, slug: 1 });
        console.log('  ✅ Created full page index');

        // Navigation index
        await safeCreateIndex(collections.sections, { showInNav: 1, navCategory: 1, page: 1 });
        console.log('  ✅ Created navigation index');

        // Unique navigation anchor index (simplified partial filter)
        await safeCreateIndex(
            collections.sections,
            { page: 1, navAnchor: 1 },
            {
                unique: true,
                partialFilterExpression: {
                    navAnchor: { $exists: true },
                    showInNav: true,
                },
                name: 'section_unique_nav_anchor',
            }
        );
        console.log('  ✅ Created unique navigation anchor index');

        // Content override index
        await safeCreateIndex(collections.sections, {
            isContentOverride: 1,
            targetPage: 1,
            isActive: 1,
        });
        console.log('  ✅ Created content override index');

        // Clean up empty slug values before creating unique index
        await collections.sections.updateMany({ slug: '' }, { $unset: { slug: 1 } });
        console.log('  ✅ Cleaned up empty slug values');

        // Layout and slug indexes
        await safeCreateIndex(collections.sections, { layout: 1, page: 1 });
        await safeCreateIndex(collections.sections, { slug: 1 }, { unique: true, sparse: true });
        console.log('  ✅ Created section layout and slug indexes');
    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
}

async function verifyIndexes() {
    console.log('\n🔍 Verifying indexes...\n');

    const collections = ['tutors', 'blogs', 'users', 'sections', 'orders'];

    for (const collectionName of collections) {
        const collection = mongoose.connection.collection(collectionName);
        const indexes = await collection.indexes();

        console.log(`📋 ${collectionName.toUpperCase()} (${indexes.length} indexes):`);
        indexes.forEach(index => {
            const keyStr = JSON.stringify(index.key);
            const name = index.name;
            console.log(`  - ${name}: ${keyStr}`);
        });
        console.log('');
    }
}

async function main() {
    console.log('🚀 Starting database index migration...\n');

    try {
        await connectToDatabase();
        await applyIndexes();
        await verifyIndexes();

        console.log('🎉 Index migration completed successfully!\n');
        console.log('📈 Performance improvements: ');
        console.log('  • Tutor searches will be significantly faster');
        console.log('  • Blog category filtering optimized');
        console.log('  • Navigation anchor uniqueness enforced');
        console.log('  • Text search capabilities enhanced');
        console.log('  • User authentication queries optimized');
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        console.log('\n🔌 Database connection closed');
        await mongoose.connection.close();
    }
}

// Run the migration
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, applyIndexes, verifyIndexes };
