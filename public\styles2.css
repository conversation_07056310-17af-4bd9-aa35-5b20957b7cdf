/* ====================================================================== */
/*                              IMPORTS                                   */
/* ====================================================================== */
/*
 * STYLES2.CSS - Main stylesheet for Tutors Alliance Scotland
 *
 * CROSS-MODULE DEPENDENCIES:
 * - Base for: All other CSS modules extend these styles
 * - Overridden by: nav.css, animation-module.css, typography-module.css
 * - Establishes: Container patterns (max-width: 1080px, margin: 2rem auto)
 *
 * LOAD ORDER: 1st (lowest specificity) - provides base styles
 * PURPOSE: Core layout, components, and responsive base patterns
 */

/* Import UI responsive module (contact buttons, hero/CTA banners) */
@import url('css/ui-responsive-module.css');

/* ====================================================================== */
/*                          BASE STYLES                                  */
/* ====================================================================== */

/* Brand palette and background */
body {
  background: #e6f0ff;
  color: #222;
  font-family: 'Segoe UI', Arial, sans-serif;
  padding-bottom: 0; /* Remove padding as we now have a static footer */
}

/* PATTERN: Standard container layout - used by ui-responsive-module.css */
/* Specificity: 0,0,1,0 each - Base container styling */
.main-image,
.two-col-content,
.icon-panel,
.aurora-buttons,
.testimonials-row,
.confidence-block,
.footer-grid {
  background: #e6f0ff;
  max-width: 1080px; /* PATTERN: Standard container width */
  margin: 2rem auto; /* PATTERN: Standard container centering */
}

/* Fade-in animations - NOW IN animation-module.css */

/* Header and navigation styles are now in header-banner.css */

/* ====================================================================== */
/*                          HERO BANNER COMPONENT                        */
/* ====================================================================== */

/**
 * Hero Banner Component
 *
 * @description Full-width banner with gradient overlay and centered content
 * @usage Applied to homepage and key landing pages for primary messaging
 * @background Uses parentAndChild.png with dark gradient overlay for readability
 * @responsive Adjusts min-height and padding on mobile (handled in ui-responsive-module.css)
 * @dependencies Requires .hero-content child element for proper text styling
 * @layout Flexbox centered, with backdrop-filter blur effect on content
 */

/* HERO full-width banner with background image */
.hero-banner {
  position: relative;
  width: 100%;
  min-height: 400px;
  margin: 2rem 0 2.5rem;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 6px 36px rgba(0, 87, 183, 0.06);
  background:
    linear-gradient(rgba(0, 27, 68, 0.7), rgba(0, 87, 183, 0.7)),
    url('/images/parentAndChild.png') center/cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
}

.hero-content {
  max-width: 800px;
  text-align: center;
  color: white;
  z-index: 2;
  padding: 2rem;
  background-color: rgba(0, 27, 68, 0.5);
  border-radius: 1rem;
  backdrop-filter: blur(5px);
}

.hero-content h1 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Two-column generic block */
.two-col-content {
  display: flex;
  gap: 2rem;
  margin: 2.5rem 0;
  padding: 2.5rem 2rem;
  border-radius: 1.5rem;
  background: #fff;
  align-items: center;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
}

.two-col-content img {
  max-width: 325px; /* Increased by 25% from 260px */
  border-radius: 1rem;
}

.two-col-content > div {
  flex: 1 1 50%;
}

.confidence-block {
  background: #e6f0ff;
  box-shadow: none;
}

/* Icon panel */
.icon-panel {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 2.5rem 0;
  padding: 2rem 0.5rem;
  gap: 0.5rem;
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
}

.icon-symbol {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 2.2rem;
  color: #0057b7;
}

.icon-symbol p {
  font-size: 1rem;
  margin: 0.5rem 0 0;
  color: #333;
}

/* ====================================================================== */
/*                          CTA BANNER COMPONENT                         */
/* ====================================================================== */

/**
 * Call-to-Action Banner Component
 *
 * @description Secondary banner for action-oriented messaging and buttons
 * @usage Placed strategically throughout pages to drive user engagement
 * @background Customizable background image with overlay support
 * @responsive Reduces min-height and adjusts padding on mobile (ui-responsive-module.css)
 * @layout Similar to hero banner but smaller min-height (200px vs 400px)
 * @positioning Can be positioned at top/middle/bottom of page content
 */

/* CTA Banner with background image */
.cta-banner {
  position: relative;
  width: 100%;
  min-height: 200px;
  margin: 2.5rem 0;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 6px 36px rgba(0, 87, 183, 0.06);
  background: linear-gradient(rgba(0, 27, 68, 0.6), rgba(0, 87, 183, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.cta-content {
  width: 100%;
  max-width: 900px;
  text-align: center;
  z-index: 2;
}

/* ====================================================================== */
/*                          BUTTON COMPONENTS                           */
/* ====================================================================== */

/**
 * Button Components System
 *
 * @description Reusable button styles for various contexts
 * @usage Throughout the site for actions, CTAs, and navigation
 * @types
 *   - .button.aurora: Primary action buttons with gradient styling
 *   - .cta-buttons: Container for call-to-action button groups
 *   - .contact-btn: Contact-specific buttons (responsive in ui-responsive-module.css)
 * @responsive Button sizing and spacing handled in ui-responsive-module.css
 * @styling Aurora gradient theme with hover effects and transitions
 */

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin: 0;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

/* Responsive adjustments for contact buttons - MOVED TO contact-responsive-module.css */

/* ====================================================================== */
/*                          CONTENT LAYOUT SYSTEM                        */
/* ====================================================================== */

/**
 * Content Layout System
 *
 * @description Core layout structure for page content organization
 * @usage Provides the foundation for all page layouts with left/right columns
 * @components
 *   - content-flex-wrapper: Main container for two-column layout
 *   - left-col: Left column (23% width) for shield/ribbon images
 *   - right-col: Right column (77% width) for main content
 * @responsive
 *   - Maintains two-column layout until 600px breakpoint
 *   - Collapses to single column on mobile portrait orientation
 *   - Hides left-col (shield/ribbons) on mobile portrait
 * @relationships Works with shield/banner positioning system
 */

/* Content Layout */
.content-flex-wrapper {
  display: flex;
  position: relative;
}

.left-col {
  flex: 0 0 23%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.right-col {
  width: 62vw;
  margin-left: auto; /* push it to the right half of the viewport */
  text-align: left; /* paragraphs left-aligned within this half */
  position: relative; /* optional if you want to position child elements absolutely */
}

/* ====================================================================== */
/*                          TUTOR GRID COMPONENT                        */
/* ====================================================================== */

/**
 * Tutor Grid Component
 *
 * @description CSS Grid layout for displaying tutor cards in organized columns
 * @usage Primary layout for tutors.js page and tutor directory displays
 * @layout
 *   - Desktop: 3 columns (repeat(3, 1fr))
 *   - Tablet (≤900px): 2 columns (repeat(2, 1fr))
 *   - Mobile Portrait (≤600px): 1 column (1fr)
 * @responsive Responsive behavior handled in ui-responsive-module.css
 * @positioning 5vw left margin for alignment with other page elements
 * @dependencies Works with .tutor-card child elements for individual tutor display
 */

/* Tutor Grid */
.tutor-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0px;
  padding: 20px;
  margin-left: 5vw;
  margin-top: 23vh; /* Push the grid further down */
  margin-left: 21vw; /* Shift the grid to the right */
  opacity: 0; /* Initially hidden */
  transition: opacity 1s ease-in-out;
  row-gap: 20px; /* Adds vertical space between the rows */
}

/* Tutor Cards */
.tutor-card {
  width: 100%;
  height: 100%;
  border: 1px solid #ddd;
  padding: 0px;
  border-radius: 0;
  background-color: rgba(255, 255, 255, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  opacity: 0; /* Initially hidden */
  transform: translateY(20px);
  transition:
    opacity 0.8s ease,
    transform 0.8s ease;
  border-bottom: 2px solid #ddd; /* Light gray bottom border */
  margin-bottom: 10px; /* Add some spacing between rows */
}

.tutor-card p.available-in.custom-style {
  display: block;
  max-width: 70%; /* Restricts the width to 70% of the card */
  margin: 0 auto; /* Centers the content horizontally */
  font-size: 0.9em; /* Reduces the font size for better fit */
  text-align: center; /* Center-aligns the text */
  white-space: normal; /* Allows text to wrap onto multiple lines */
  word-wrap: break-word; /* Ensures long words break to fit */
  line-height: 1.3; /* Adjusts the line spacing for readability */
  color: #555; /* Makes the color a bit softer */
  padding: 0;
}

.tutor-card::before {
  content: '';
  background: url('/images/flag.PNG') no-repeat center center;
  opacity: 0.4; /* Increase opacity to show more blue */
  background-blend-mode: overlay; /* Blend mode to enhance blue visibility */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* Shield and Banner animations - NOW IN animation-module.css */

/* Keyframes growLeft - NOW IN animation-module.css */

/* Page-specific shield and banner positioning overrides */

/* Tutor Directory page - positioned to avoid content overlap */
.tutor-directory-page #imageShield,
.tutor-directory-page #imageBanner {
  position: absolute;
}

.tutor-directory-page #imageShield {
  top: 10% !important;
  left: 60% !important;
  z-index: 9999 !important; /* High z-index to stay above all content */
}

.tutor-directory-page #imageBanner {
  top: 12% !important; /* Closer to shield, not at bottom */
  left: 60% !important;
  z-index: 9998 !important; /* Second highest z-index, behind shield but above content */
}

/* About page - positioned to right to avoid text overlap */
.about-page #imageShield {
  left: 73% !important;
  z-index: 9999 !important;
}

.about-page #imageBanner {
  left: 73% !important;
  z-index: 9998 !important;
}

/* Contact page - positioned to right to avoid form overlap */
.contact-page #imageShield {
  left: 73% !important;
  z-index: 9999 !important;
}

.contact-page #imageBanner {
  left: 73% !important;
  z-index: 9998 !important;
}

/* Tutor Membership page - positioned to avoid hero section overlap */
body[data-page='tutorMembership'] #imageShield {
  top: 5% !important;
  left: 65% !important;
  z-index: 9999 !important; /* Very high z-index to stay above all fade-in content */
}

body[data-page='tutorMembership'] #imageBanner {
  top: 7% !important;
  left: 65% !important;
  z-index: 9998 !important; /* Second highest z-index, behind shield but above content */
}

/* Fix stacking context issue: mission-row containing shield/banner needs higher z-index than later sections */
body[data-page='tutorMembership'] .mission-row.fade-in-section {
  z-index: 100 !important; /* Higher than other fade-in sections to keep shield/banner visible */
  position: relative !important; /* Ensure z-index takes effect */
}

/* Push down first content section to avoid banner overlap */
body[data-page='tutorMembership'] .two-col-content:first-of-type {
  margin-top: 6rem !important; /* Extra space to clear banner ribbons */
}

/* Parents page - positioned to avoid content overlap */
body[data-page='parents'] #imageShield {
  left: 70% !important;
  z-index: 9999 !important;
}

body[data-page='parents'] #imageBanner {
  left: 70% !important;
  z-index: 9998 !important;
}

/* Fix stacking context issue: mission-row containing shield/banner needs higher z-index than later sections */
body[data-page='parents'] .mission-row.fade-in-section {
  z-index: 100 !important; /* Higher than other fade-in sections to keep shield/banner visible */
  position: relative !important; /* Ensure z-index takes effect */
}

/* Partnerships page - positioned to avoid content overlap */
body[data-page='partnerships'] #imageShield {
  left: 70% !important;
  z-index: 9999 !important;
}

body[data-page='partnerships'] #imageBanner {
  left: 70% !important;
  z-index: 9998 !important;
}

/* Tutors Zone page - positioned to avoid content overlap */
body[data-page='tutorszone'] #imageShield {
  left: 70% !important;
  z-index: 9999 !important;
}

body[data-page='tutorszone'] #imageBanner {
  left: 70% !important;
  z-index: 9998 !important;
}

/* ====================================================================== */
/*                          DYNAMIC PAGES SYSTEM                        */
/* ====================================================================== */

/**
 * Dynamic Pages System
 *
 * @description Specialized styling for dynamically generated content pages
 * @usage Applied to pages created through the admin content management system
 * @structure
 *   - .dynamic-page: Parent class for all dynamic pages
 *   - .dynamic-section-container: Container for dynamic sections (80% width)
 *   - .dyn-block: Individual content blocks within dynamic sections
 * @responsive
 *   - Maintains 80% width until 600px breakpoint
 *   - Adjusts to 95% width on very small screens
 *   - Proportionally reduces padding on mobile breakpoints
 * @differences
 *   - No shield/ribbons (explicitly hidden)
 *   - Larger padding (3.2rem vs 2rem) for better content spacing
 *   - Special handling for two-column layouts
 */

/* Dynamic pages use default positioning from main rules above */

/* Dynamic pages only - hide shield and ribbons completely */
.dynamic-page .main-shield,
.dynamic-page .main-ribbons {
  display: none !important;
}

/* Dynamic pages only - make sections 80% of page width with proportional height */
.dynamic-page .dynamic-section-container {
  max-width: 80% !important;
  width: 80% !important;
  margin: 0 auto !important; /* Center the container horizontally */
}

.dynamic-page .dyn-block {
  max-width: none !important;
  width: 100% !important;
  /* Increase padding proportionally to maintain visual balance */
  padding: 3.2rem !important; /* Increased from 2rem (2 * 1.6 = 3.2) */
}

.dynamic-page .dyn-block.two-col-content {
  padding: 4rem 3.2rem !important; /* Proportional increase for two-column sections */
}

/* Ensure images scale proportionally within the larger sections */
.dynamic-page .dyn-block img {
  max-width: 100% !important;
}

.dynamic-page .dyn-block.two-col-content img,
.dynamic-page .two-col-content.dyn-block img {
  max-width: 782px !important; /* Keep the 77% larger image size we set earlier */
}

/* Responsive adjustments for dynamic pages - maintain 80% width but adjust padding */
@media (max-width: 900px) {
  .dynamic-page .dyn-block {
    padding: 2.4rem !important; /* Proportionally reduced for mobile (3.2 * 0.75 = 2.4) */
  }

  .dynamic-page .dyn-block.two-col-content {
    padding: 3rem 2.4rem !important; /* Proportionally reduced for mobile */
  }
}

@media (max-width: 600px) {
  .dynamic-page .dynamic-section-container {
    width: 95% !important; /* Slightly narrower on very small screens */
  }

  .dynamic-page .dyn-block {
    padding: 2rem !important; /* Further reduced for very small screens */
  }

  .dynamic-page .dyn-block.two-col-content {
    padding: 2.5rem 2rem !important; /* Further reduced for very small screens */
  }
}

/* Show class for animations */
.show {
  opacity: 1;
}

/* Responsive adjustments for tutor grid */
@media (max-width: 900px) {
  .tutor-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Single column for portrait on mobile */
@media (max-width: 600px) and (orientation: portrait) {
  .tutor-grid {
    grid-template-columns: 1fr;
  }

  /* Hide shield and ribbons on portrait mobile */
  .left-col {
    display: none !important;
  }

  /* Adjust layout for portrait mode */
  .right-col {
    width: 100%;
    float: none;
    margin: 0 auto;
    padding: 0 15px;
  }
}

/* ====================================================================== */
/*                          NAVIGATION SYSTEM                           */
/* ====================================================================== */

/**
 * Navigation System
 *
 * @description Main site navigation with responsive behavior
 * @usage Primary navigation bar appearing on all pages
 * @structure
 *   - .main-nav: Container with navy background (#001B44)
 *   - .main-nav ul: Flex container for navigation items
 *   - .main-nav ul li: Individual navigation items with borders
 *   - .main-nav ul li a: Navigation links with hover effects
 * @responsive
 *   - Desktop: Horizontal flex layout with centered items
 *   - ≤900px: Grid layout with auto-fit columns (min 150px)
 *   - Portrait: 2-column grid layout
 * @styling
 *   - Light blue borders (#ADD8E6) between items
 *   - Hover effect with darker blue background (#003F8F)
 *   - White text with proper padding and font sizing
 */

/* Navigation Bar Styling */
/* Specificity: 0,0,1,0 - Overridden by nav.css .main-nav (same specificity, later load order) */
.main-nav {
  background-color: #001b44; /* a deep navy, or #003F8F, etc. */
}

.main-nav ul {
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
}

.main-nav ul li {
  position: relative;
  border-left: 1px solid #add8e6;
}
.main-nav ul li:first-child {
  border-left: none;
}

.main-nav ul li a {
  color: #fff;
  text-decoration: none;
  padding: 14px 20px;
  display: block;
  font-size: 1.1em;
}

.main-nav ul li a:hover {
  background-color: #003f8f; /* highlight on hover */
}

/* Enhanced Navigation Styling */
.nav-item {
  position: relative;
  border-left: 1px solid #add8e6;
  transition: background-color 0.3s ease;
}

.nav-item:first-child {
  border-left: none;
}

.nav-link {
  color: #fff;
  text-decoration: none;
  padding: 14px 20px;
  display: block;
  font-size: 1.1em;
  transition: background-color 0.3s ease;
}

.nav-link:hover {
  background-color: #003f8f;
}

.nav-dropdown {
  display: none;
  position: absolute;
  background-color: #001b44;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  top: 100%;
  left: 0;
  border-radius: 0 0 4px 4px;
}

.nav-dropdown a {
  color: white;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  text-align: left;
  border-bottom: 1px solid #003f8f;
  transition: background-color 0.3s ease;
}

.nav-dropdown a:hover {
  background-color: #003f8f;
}

.custom-pages-dropdown:hover .nav-dropdown {
  display: block;
}

/* Tutor Images - ensure proper centering */
.tutor-image {
  max-width: 100%;
  display: block; /* block-level so it starts at top of card */
  margin-top: 0;
  height: 57vh;
  object-fit: cover;
  border-radius: 1px;
}

.tutor-card p.available-in.custom-style {
  display: block;
  max-width: 70%; /* Restricts the width to 70% of the card */
  margin: 0 auto; /* Centers the content horizontally */
  font-size: 0.9em; /* Reduces the font size for better fit */
  text-align: center; /* Center-aligns the text */
  white-space: normal; /* Allows text to wrap onto multiple lines */
  word-wrap: break-word; /* Ensures long words break to fit */
  line-height: 1.3; /* Adjusts the line spacing for readability */
  color: #555; /* Makes the color a bit softer */
  padding: 0;
}

.tutor-card img {
  max-width: 100%;
  display: block; /* block-level so it starts at top of card */
  margin-top: 0;
  height: 57vh;
  object-fit: cover;
  object-position: center; /* Center the image within its container */
  border-radius: 1px;
}

.tutor-card::before {
  content: '';
  background: url('/images/flag.PNG') no-repeat center center;
  opacity: 0.4; /* Increase opacity to show more blue */
  background-blend-mode: overlay; /* Blend mode to enhance blue visibility */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.badge-item {
  list-style: none;
  font-weight: bold;
  margin-bottom: 5px;
}

.badge-tick {
  color: #800080;
  font-size: 1.2em;
  margin-left: 5px; /* Small spacing between the badge description and the tick */
}

/* Purple Pound Symbols */
.purple-pound {
  color: #800080;
  font-weight: bold;
}

/* ====================================================================== */
/*                          TESTIMONIALS SYSTEM                         */
/* ====================================================================== */

/**
 * Testimonials System
 *
 * @description Multiple testimonial display formats for social proof
 * @usage Throughout the site to showcase parent/student feedback
 * @components
 *   - .testimonials-row: Simple flex row for basic testimonial cards
 *   - .testimonials-bg-section: Full-width background section
 *   - .testimonials-overlay-card: White card overlay on background
 *   - .testimonials-laced: Overlapping quote layout with staggered positioning
 *   - .testimonial-quote-card: Individual quote cards within laced layout
 * @responsive
 *   - Desktop: Horizontal layout with overlapping quotes
 *   - ≤900px: Stacked layout with increased min-height
 *   - Portrait: Further height increase and font size reduction
 * @styling
 *   - Light backgrounds with subtle shadows
 *   - Rounded corners for cards (border-radius: 1.2-2rem)
 *   - Specific positioning for overlapping effect
 */

/* Testimonials row */
.testimonials-row {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin: 2.5rem 0;
}

.testimonial-card {
  background: #fff;
  border-radius: 1rem;
  box-shadow: 0 2px 12px rgba(200, 162, 200, 0.1);
  padding: 1.5rem 1.2rem;
  max-width: 320px;
  min-width: 220px;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.testimonial-card span {
  color: #0057b7;
  margin-top: 0.75rem;
  font-size: 0.98rem;
}

.business-insurance {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 30px;
}

.insurance-logo {
  width: 120px;
  height: auto;
  margin-bottom: 15px;
}

.business-insurance p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.insurance-link {
  color: #0057b7; /* Lilac color for the link */
  font-size: 0.9rem;
  margin-top: 5px;
}

.website-url {
  font-weight: bold;
  margin-top: 20px;
}

/* Dynamic Sections Styling */
.dynamic-section-container {
  max-width: 1080px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.dyn-block {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(20px);
}

.dyn-block h2 {
  color: #0057b7;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.dyn-content {
  margin-bottom: 1.5rem;
}

.dyn-image-container {
  width: 100%;
  margin-bottom: 1.5rem;
  border-radius: 1rem;
  overflow: hidden;
}

.dyn-image-container img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
  margin: 0 auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dyn-image-container img:hover {
  transform: scale(1.02);
}

.dynamic-sections-separator,
.dynamic-sections-separator-top,
.dynamic-sections-separator-middle {
  height: 2rem;
}

/* ====================================================================== */
/*                          RESPONSIVE STYLES                           */
/* ====================================================================== */

/**
 * Responsive Styles System
 *
 * @description Media queries for responsive layout adjustments
 * @breakpoints
 *   - 900px: Primary tablet/small screen breakpoint
 *   - 768px: Secondary breakpoint for specific components
 *   - 700px: Breakpoint for overlay cards
 *   - 600px: Mobile breakpoint for smallest screens
 *   - 480px: Extra small device breakpoint
 * @orientations
 *   - portrait: Special handling for portrait orientation
 *   - landscape: Special handling for landscape orientation
 * @components Affects multiple components:
 *   - Layout (flex → column)
 *   - Grid columns (3 → 2 → 1)
 *   - Font sizes (reduction)
 *   - Padding/margins (proportional reduction)
 *   - Component visibility (selective hiding)
 * @note Some responsive styles moved to ui-responsive-module.css
 */

/* Responsive tweaks */
@media (max-width: 900px) {
  /* Hero and CTA banner responsive styles - MOVED TO contact-responsive-module.css */

  .two-col-content {
    flex-direction: column;
    padding: 1.2rem 0.8rem;
  }

  .icon-panel {
    flex-direction: column;
    align-items: stretch;
    gap: 1.2rem;
  }

  .testimonials-row {
    flex-direction: column;
    align-items: stretch;
  }

  /* Responsive static footer */
  .static-footer-container {
    flex-direction: column;
  }

  .static-footer-left {
    padding-right: 0;
    margin-bottom: 30px;
  }

  .static-footer-right {
    align-items: center;
    text-align: center;
  }

  .business-insurance {
    align-items: center;
  }

  /* Responsive button adjustments */
  .button-group {
    gap: 0.8rem;
  }

  .button.aurora {
    padding: 0.8em 1.8em;
    font-size: 1rem;
  }

  /* Responsive adjustments for parent, tutor, pupil, strive, newsletter, and dynamic boxes */
  .parents-box h2,
  .tutor-box h2,
  .pupil-box h2,
  .strive-box h2,
  .newsletter-box h2,
  .dyn-block h2 {
    font-size: 1.5em;
  }

  /* Responsive adjustments for dynamic sections */
  .dynamic-section-container {
    padding: 0 0.8rem;
  }

  .dyn-block {
    padding: 1.5rem;
  }

  .dyn-image-container {
    margin-bottom: 1rem;
  }

  #dynamicSections,
  #dynamicSectionsTop,
  #dynamicSectionsMiddle {
    margin: 1.5rem auto;
  }
  #dynamicSectionsMiddle {
    margin-top: 3rem;
    width: 100%;
    max-width: 1080px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
  }

  .dyn-block {
    padding: 1rem;
    margin: 2rem auto;
  }

  .parents-box ul,
  .tutor-box ul,
  .pupil-box ul,
  .newsletter-box ul,
  .dyn-block ul {
    padding-left: 0;
    margin: 1rem auto;
  }

  .parents-box li,
  .tutor-box li,
  .pupil-box li,
  .newsletter-box li {
    padding-left: 1.5rem;
    margin-bottom: 0.6rem;
  }

  /* Responsive newsletter adjustments */
  .newsletter-cta {
    margin: 1.5rem auto 0.5rem;
  }

  .subscribe-image {
    max-width: 160px;
  }

  /* Responsive STRIVE values */
  .strive-values {
    gap: 0.8rem;
    margin: 1.5rem auto;
  }

  .strive-value {
    padding: 0.8rem;
    gap: 1rem;
  }

  .strive-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.5rem;
  }

  .strive-value p {
    font-size: 0.9rem;
  }

  /* Responsive mission statement */
  .mission-statement {
    font-size: 2.5em; /* Smaller on mobile but still larger than original */
  }

  .strive-small-card {
    position: static;
    margin: 2.5rem auto;
    transform: none;
    right: auto;
    left: auto;
  }

  /* Header: move h1 and buttons to the right */
  header {
    flex-direction: column !important;
    align-items: flex-end !important;
    padding: 18px !important;
  }
  header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    text-align: right !important;
    width: 100%;
  }
  .header-links {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    text-align: right !important;
    width: 100%;
  }
  /* Restore previous questionPeriod.png position */
  .parent-img-list-left-period,
  .parent-img-list-right-period {
    left: 3px !important;
    top: -13px !important;
  }
  /* Center all sections and images in portrait view */
  .zone-list-row,
  .parents-box,
  .tutor-box,
  .pupil-box {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
    align-items: center !important;
    justify-content: center !important;
  }
  .parent-list,
  .tutor-list,
  .pupil-list {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
  }
  .parent-img-list,
  .tutor-img-list,
  .pupil-img-list {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

/* move the CURL 5?px right */
.parent-stack-left .parent-img-list-left {
  position: relative; /* create a new positioning context   */
  left: 21px;
}

/* Full viewport: restore previous questionPeriod.png positions */
.parent-stack-right .parent-img-list-right-period {
  left: -61px;
}

/* Mission row layout */
.mission-row {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  max-width: 1080px;
  margin: 0 auto;
  padding: 40px;
  flex-direction: row-reverse;
}

.left-col {
  flex: 0 0 23%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.main-shield {
  max-width: 250px;
  width: 100%;
  margin-bottom: 0.5rem;
  opacity: 0;
  transform: translateX(20px);
  animation: growRight 0.8s ease-out forwards;
}

.main-ribbons {
  max-width: 200px;
  width: 100%;
  margin-top: 257px;
  margin-left: 0.42rem;
  opacity: 0;
  transform: translateX(20px);
  animation: growRight 0.8s ease-out 0.3s forwards;
}

@keyframes growRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.right-col {
  flex: 1;
  text-align: left;
  padding: 0 20px;
}

.about-us-landing {
  max-width: 800px;
  margin: 0 auto;
}

@keyframes fadeGrow {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade-in animations for sections */
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition:
    opacity 0.6s ease-out,
    transform 0.6s ease-out;
  will-change: opacity, transform;
  visibility: visible !important; /* Ensure elements are visible */
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure dynamic blocks also fade in */
/* Dynamic Sections Container */
#dynamicSections {
  margin-top: 2rem; /* Reduced space above dynamic sections to match static section spacing */
  padding-top: 1rem; /* Reduced padding */
  width: 100%;
  max-width: 1080px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  clear: both; /* Ensure it clears any floated elements */
  display: block; /* Ensure it's a block element */
  float: none; /* Prevent floating */
  z-index: 1; /* Ensure proper stacking */
}

/* Clear separator before dynamic sections */
.dynamic-sections-separator {
  clear: both;
  width: 100%;
  height: 80px; /* Significant space */
  display: block;
  margin: 0;
  padding: 0;
  position: relative;
}

/* Remove visual indicator before the dynamic sections */
#dynamicSections::before {
  content: '';
  display: block;
  margin-bottom: 2rem;
}

.dyn-block {
  opacity: 0;
  transform: translateY(20px);
  transition:
    opacity 0.6s ease-out,
    transform 0.6s ease-out;
  will-change: opacity, transform;
  visibility: visible !important; /* Ensure elements are visible */
  max-width: 900px;
  margin: 3rem auto;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 1rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  position: relative; /* Ensure proper stacking */
  z-index: 1; /* Ensure proper stacking */
  clear: both; /* Clear any floats */
}

.dyn-block.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Style the dynamic section images */
.dyn-block img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Ensure two-column layout images maintain proper sizing */
.dyn-block.two-col-content img,
.two-col-content.dyn-block img {
  max-width: 782px !important; /* Increased by 77% from 442px (442 * 1.77 = 782.34) */
  border-radius: 1rem !important;
  transform-origin: left top; /* Expand left and downward */
}

/* Style the dynamic section headings */
.dyn-block h2 {
  color: #0057b7;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  text-align: center;
}

/* Style the dynamic section paragraphs */
.dyn-block p {
  line-height: 1.6;
  color: #333;
  margin-bottom: 1rem;
}

/* Add a small delay between sections for a staggered effect */
.fade-in-section:nth-child(2) {
  transition-delay: 0.1s;
}
.fade-in-section:nth-child(3) {
  transition-delay: 0.2s;
}
.fade-in-section:nth-child(4) {
  transition-delay: 0.3s;
}
.fade-in-section:nth-child(5) {
  transition-delay: 0.4s;
}

/* Style parent, tutor, pupil, strive, and newsletter sections to match other sections */
.parents-zone-section,
.tutor-zone-section,
.pupil-zone-section,
.strive-zone-section,
.newsletter-zone-section {
  max-width: 1080px;
  margin: 2rem auto;
  padding: 2.5rem 2rem;
  border-radius: 1.5rem;
  background: #fff;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
}

.parents-box,
.tutor-box,
.pupil-box,
.strive-box,
.newsletter-box {
  background: #b8d4ff; /* lighter lilac */
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  text-align: center;
  padding: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.parents-box h2,
.tutor-box h2,
.pupil-box h2,
.strive-box h2,
.newsletter-box h2 {
  color: #0057b7;
  margin-bottom: 1rem;
  font-size: 1.8em;
}

.parents-box ul,
.tutor-box ul,
.pupil-box ul,
.newsletter-box ul {
  text-align: left;
  max-width: 600px;
  margin: 1.5rem auto;
  list-style-type: none;
  padding-left: 0;
}

.parents-box li,
.tutor-box li,
.pupil-box li,
.newsletter-box li {
  margin-bottom: 0.8rem;
  padding-left: 2rem;
  position: relative;
  line-height: 1.5;
}

.parents-box p,
.tutor-box p,
.pupil-box p,
.strive-box p,
.newsletter-box p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Newsletter specific styles */
.newsletter-cta {
  margin: 2rem auto 1rem;
}

.subscribe-link {
  display: inline-block;
  transition:
    transform 0.3s ease,
    filter 0.3s ease;
}

.subscribe-link:hover {
  transform: translateY(-5px);
}

.subscribe-image {
  max-width: 200px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 87, 183, 0.15);
  transition: filter 0.3s ease;
}

.subscribe-link:hover .subscribe-image {
  filter: hue-rotate(-10deg) saturate(1.5) brightness(1.05);
  box-shadow: 0 6px 16px rgba(200, 162, 200, 0.3);
}

/* ====================================================================== */
/*                          STRIVE COMPONENTS                           */
/* ====================================================================== */

/**
 * STRIVE Components System
 *
 * @description Components for displaying STRIVE values and mission content
 * @usage Core branding elements showcasing organizational values
 * @components
 *   - .strive-values: Container for all STRIVE value items
 *   - .strive-value: Individual value item with icon and text
 *   - .strive-icon: Circular icon container for value symbols
 *   - .strive-bg-section: Full-width background section
 *   - .strive-overlay-card: White content card over background
 *   - .strive-small-card: Smaller variant for specific layouts
 * @responsive
 *   - Desktop: Full layout with proper spacing
 *   - ≤900px: Reduced icon sizes and gap spacing
 *   - Mobile: Static positioning instead of absolute
 * @styling
 *   - Light blue color scheme matching brand palette
 *   - Hover effects with subtle transforms and shadows
 *   - Background image: childStudy.PNG with gradient overlay
 */

/* STRIVE values styling */
.strive-values {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  max-width: 700px;
  margin: 2rem auto;
}

.strive-value {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  text-align: left;
  background-color: #f8f9ff;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 87, 183, 0.05);
  transition: transform 0.2s ease-out;
}

.strive-value:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 87, 183, 0.1);
}

.strive-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #0057b7, #001b44);
  color: white;
  border-radius: 50%;
  font-size: 1.8rem;
  font-weight: bold;
  flex-shrink: 0;
}

.strive-value p {
  margin: 0;
  flex: 1;
}

/* Center all sections and add more padding between them */
.two-col-content,
.icon-panel,
.testimonials-row,
.confidence-block,
.footer-grid,
.parents-zone-section,
.tutor-zone-section,
.pupil-zone-section,
.strive-zone-section,
.newsletter-zone-section {
  max-width: 1080px;
  margin: 3.6rem auto;
  padding: 2.5rem 2rem;
  border-radius: 1.5rem;
  background: #fff;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Hero and CTA banners are full-width and have their own styling */
.hero-banner,
.cta-banner {
  max-width: 1080px;
  margin: 3.6rem auto;
  border-radius: 1.5rem;
}

/* Ensure content within sections is properly centered */
.main-image .info,
.two-col-content > div,
.icon-panel,
.testimonials-row,
.confidence-block > div {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* Adjust the two-column content to maintain readability while being centered */
.two-col-content {
  flex-direction: row;
  gap: 2rem;
  text-align: left;
}

.two-col-content > div {
  flex: 1 1 50%;
}

/* Ensure images in sections are properly sized and centered */
.main-image img,
.two-col-content img,
.confidence-block img {
  max-width: 100%;
  height: auto;
  border-radius: 1rem;
  margin: 0 auto;
}

/* About section H2 color to light blue */
.two-col-content h2 {
  color: #7ca7d9;
}

/* Dynamic page content styling */
.page-content {
  line-height: 1.6;
}

.page-content p {
  margin-bottom: 1rem;
}

/* Single column layout for dynamic pages without images */
.two-col-content.single-column {
  justify-content: center;
}

.two-col-content.single-column > div:first-child {
  max-width: 800px;
  text-align: left;
}

/* Footer style with tartan/tile pattern and fade-in */
.site-footer {
  width: 100%;
  min-height: 48px;
  background-image: url('/images/bannerBackground.PNG');
  background-repeat: repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
  margin: 0;
  border-top: 0;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100;
  transition:
    opacity 0.6s ease-out,
    transform 0.6s ease-out;
  opacity: 0;
  transform: translateY(20px);
}
.site-footer.is-visible {
  opacity: 1;
  transform: translateY(0);
}
.site-footer .footer-icons {
  display: flex;
  gap: 2.5rem;
  justify-content: center;
  align-items: center;
}
.site-footer .footer-icons a {
  color: #fff;
  font-size: 2.5rem;
  transition: color 0.2s;
}
.site-footer .footer-icons a:hover {
  color: #c8a2c8;
}

/* Header: reduce vertical height by 15% (from 36px to 30.6px) */
header {
  background-image: url('/images/bannerShield2.png'), url('/images/bannerBackground.PNG');
  background-repeat: no-repeat, repeat;
  background-position:
    20px 20px,
    left top;
  background-size:
    100px auto,
    150px auto;
  padding: 30.6px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* STRIVE and TESTIMONIALS full-width background sections */
.strive-bg-section,
.testimonials-bg-section {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  min-height: 380px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  overflow: hidden;
}

/* Dynamic testimonials now use standard dyn-block pattern - no special full-width rules needed */

/* Styling for dynamic testimonials within dyn-block containers */
/* Apply testimonials background to dynamic testimonial sections */
.dyn-block:has(.testimonials-container) {
  background:
    linear-gradient(rgba(255, 255, 255, 0.65), rgba(255, 255, 255, 0.55)),
    url('/images/parentAndChild.png') center/cover no-repeat;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dyn-block .testimonials-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.dyn-block .testimonial-quote-card {
  position: relative !important; /* Override any absolute positioning */
  margin: 0 auto !important;
  max-width: 100% !important;
  width: auto !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  /* Apply same styling as static testimonials */
  background: rgba(255, 255, 255, 0.93) !important;
  box-shadow: 0 2px 12px rgba(200, 162, 200, 0.13) !important;
  border-radius: 1.5rem 1.5rem 3.5rem 1.5rem !important;
  padding: 1.2rem 1.1rem !important;
  color: #222 !important;
  font-size: 1.08rem !important;
}

.strive-bg-section {
  background:
    linear-gradient(rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.5)),
    url('/images/childStudy.PNG') center/cover no-repeat;
  position: relative;
  min-height: 800px;
  height: auto;
  display: block;
}

.testimonials-bg-section {
  background:
    linear-gradient(rgba(255, 255, 255, 0.65), rgba(255, 255, 255, 0.55)),
    url('/images/parentAndChild.png') center/cover no-repeat;
}

.strive-overlay-card,
.testimonials-overlay-card,
.faq-overlay-card {
  background: #fff;
  max-width: 900px;
  width: 90vw;
  margin: 3.5rem auto;
  padding: 2.5rem 2rem;
  box-shadow: 0 6px 36px rgba(0, 87, 183, 0.1);
  border-radius: 2rem;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Override flex display for team sections to allow grid layout */
.strive-overlay-card:has(.team-grid),
.strive-team-card {
  display: block !important;
  text-align: initial;
  border-radius: 2rem !important; /* Ensure all corners are equally rounded */
  overflow: visible !important; /* Ensure rounded corners aren't clipped */
  position: relative !important; /* Ensure proper positioning context */
}

/* Additional debugging - force border-radius on all corners specifically */
.strive-team-card {
  border-top-left-radius: 2rem !important;
  border-top-right-radius: 2rem !important;
  border-bottom-left-radius: 2rem !important;
  border-bottom-right-radius: 2rem !important;
}

/* Fix the container overflow issue that clips rounded corners */
.dynamic-section-container:has(.strive-team-card),
.dynamic-section-container .strive-team-card {
  overflow-x: visible !important;
  overflow-y: visible !important;
  overflow: visible !important;
}

/* Apply the same rounded corner fix to standard dynamic sections */
.dyn-block {
  border-radius: 1.5rem !important; /* Ensure all corners are equally rounded */
  overflow: visible !important; /* Ensure rounded corners aren't clipped */
  position: relative !important; /* Ensure proper positioning context */
}

/* Additional debugging - force border-radius on all corners specifically for standard sections */
.dyn-block {
  border-top-left-radius: 1.5rem !important;
  border-top-right-radius: 1.5rem !important;
  border-bottom-left-radius: 1.5rem !important;
  border-bottom-right-radius: 1.5rem !important;
}

/* Fix the container overflow issue that clips rounded corners for standard sections */
.dynamic-section-container:has(.dyn-block),
.dynamic-section-container .dyn-block {
  overflow-x: visible !important;
  overflow-y: visible !important;
  overflow: visible !important;
}

/* Pronounced curves */
.curve-bottom-left {
  border-bottom-left-radius: 5rem 7rem !important;
}

.curve-bottom-right {
  border-bottom-right-radius: 5rem 7rem !important;
}

/* Responsive adjustments for overlay cards */
@media (max-width: 700px) {
  .strive-overlay-card,
  .testimonials-overlay-card,
  .faq-overlay-card {
    padding: 1.2rem 0.5rem;
    border-radius: 1.2rem;
  }
  .curve-bottom-left {
    border-bottom-left-radius: 2.5rem 3.5rem !important;
  }
  .curve-bottom-right {
    border-bottom-right-radius: 2.5rem 3.5rem !important;
  }
}

/* STRIVE overlay card smaller and centered */
.strive-small-card {
  max-width: 520px;
  padding: 1.5rem 1.2rem;
  margin: 0;
  box-shadow: 0 4px 18px rgba(0, 87, 183, 0.1);
  position: absolute;
  top: 50%;
  right: 10vw;
  transform: translateY(-50%);
}

/* Testimonials laced style */
.testimonials-laced {
  position: relative;
  min-height: 420px;
  padding: 0;
}
.testimonial-quote-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.93);
  box-shadow: 0 2px 12px rgba(200, 162, 200, 0.13);
  border-radius: 1.5rem 1.5rem 3.5rem 1.5rem;
  padding: 1.2rem 1.1rem;
  max-width: 320px;
  min-width: 180px;
  font-size: 1.08rem;
  color: #222;
  z-index: 2;
  text-align: left;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Base positioning rules for testimonial cards - DESKTOP VIEW */
.testimonials-laced {
  position: relative; /* Establishes the positioning container for the absolute children */
  min-height: 550px; /* Ensures the container has height so cards don't overflow */
}

.testimonials-laced .testimonial-quote-card {
  position: absolute; /* THIS IS THE CRUCIAL MISSING PIECE */
  max-width: 350px; /* A good default max-width for the cards */
}

/* Desktop positioning for testimonial cards */
.testimonials-laced .card-1 {
  top: 10%;
  left: 12%;
}

.testimonials-laced .card-2 {
  top: 40%;
  right: 10%;
}

.testimonials-laced .card-3 {
  bottom: 12%;
  left: 30%;
}
.testimonial-quote-card span {
  color: #0057b7;
  font-size: 0.98rem;
  display: block;
  margin-top: 0.7rem;
}

/* FAQ section styling */
.faq-section {
  margin: 3.6rem auto;
  max-width: 1080px;
}
.faq-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}
.faq-item {
  background-color: rgba(184, 212, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: left;
  transition: transform 0.2s ease-out;
}
.faq-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 87, 183, 0.1);
}
.faq-item h3 {
  color: #0057b7;
  margin-bottom: 0.8rem;
  font-size: 1.2rem;
}
.faq-item p {
  margin: 0;
  line-height: 1.6;
}

/* Newsletter subscribe link lilac hover */
.subscribe-link.newsletter-blue-hover:hover .subscribe-image {
  filter: none;
  box-shadow: 0 6px 16px #7ca7d9;
  border: 2px solid #7ca7d9;
}

/* Parent image row with question marks */
.parent-img-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.2rem;
}
.parent-img-row p {
  flex: 1 1 0;
  margin: 0;
  font-size: 1.15rem;
}
.questionmark-stack {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 48px;
}
.question-curl {
  width: 48px;
  height: auto;
  display: block;
}
.question-period {
  width: 36px;
  height: auto;
  margin-top: -10px;
  display: block;
}
.parent-img-left {
  margin-right: 0.5rem;
  transform: rotate(-35deg);
}
.parent-img-right {
  margin-left: 0.5rem;
  transform: rotate(35deg);
}

/* Tutor image row with legos */
.tutor-img-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.2rem;
}
.tutor-img-row p {
  flex: 1 1 0;
  margin: 0;
  font-size: 1.15rem;
}
.tutor-img-left,
.tutor-img-right {
  width: 54px;
  height: auto;
  display: block;
}
.tutor-img-left {
  margin-right: 0.5rem;
}
.tutor-img-right {
  margin-left: 0.5rem;
}

/* Pupil image row with WeAreTheFuture */
.pupil-img-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.2rem;
}
.pupil-img-row p {
  flex: 1 1 0;
  margin: 0;
  font-size: 1.15rem;
}
.pupil-img-left,
.pupil-img-right {
  width: 60px;
  height: auto;
  display: block;
}
.pupil-img-left {
  margin-right: 0.5rem;
}
.pupil-img-right {
  margin-left: 0.5rem;
}

@media (max-width: 900px) {
  .parent-img-row,
  .tutor-img-row,
  .pupil-img-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  .parent-img-left,
  .tutor-img-left,
  .pupil-img-left {
    margin: 0 auto 0.5rem auto;
    display: block;
  }
  .parent-img-right,
  .tutor-img-right,
  .pupil-img-right {
    display: none;
  }
  .questionmark-stack {
    min-width: 0;
  }
}

.zone-gradient-bg {
  position: relative;
  padding: 2.5rem 1.5rem;
  display: flex;
  align-items: stretch;
  justify-content: center;
  background: linear-gradient(135deg, #0057b7 0%, #001b44 100%);
  border-radius: 1.7rem;
  margin-bottom: 2.5rem;
}
.parent-gradient-bg,
.tutor-gradient-bg,
.pupil-gradient-bg {
  background: linear-gradient(135deg, #0057b7 0%, #001b44 100%);
}
.parents-box,
.tutor-box,
.pupil-box {
  background: #b8d4ff;
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  text-align: center;
  padding: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* OUTER IMAGE POSITIONING */
.parent-img-outer,
.tutor-img-outer,
.pupil-img-outer {
  position: absolute;
  top: 55%;
  transform: translateY(-50%);
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: none;
}
.parent-img-left {
  left: 0.5rem;
}
.parent-img-right {
  right: 0.5rem;
}
.tutor-img-left {
  left: 0.5rem;
}
.tutor-img-right {
  right: 0.5rem;
}
.pupil-img-left {
  left: 0.5rem;
}
.pupil-img-right {
  right: 0.5rem;
}

/* DOUBLE SIZE IMAGES */
.big-questionmark .question-curl {
  width: 96px;
  height: auto;
}
.big-questionmark .question-period {
  width: 72px;
  height: auto;
  margin-top: 18px;
}
.big-tutor-img {
  width: 108px;
  height: auto;
}
.big-pupil-img {
  width: 120px;
  height: auto;
}

/* INCREASE GAP BETWEEN CURL AND PERIOD */
.questionmark-stack {
  gap: 18px;
}

/* Responsive for <900px: only show left image, reduce size */
@media (max-width: 900px) {
  .zone-gradient-bg {
    padding: 1.2rem 0.5rem;
  }
  .parent-img-right,
  .tutor-img-right,
  .pupil-img-right {
    display: none !important;
  }
  .parent-img-left,
  .tutor-img-left,
  .pupil-img-left {
    left: 50%;
    transform: translate(-50%, 0);
    top: 92%;
  }
  .big-questionmark .question-curl {
    width: 54px;
  }
  .big-questionmark .question-period {
    width: 40px;
    margin-top: 8px;
  }
  .big-tutor-img {
    width: 54px;
  }
  .big-pupil-img {
    width: 60px;
  }
  .tutor-img-list tutor-img-list-right {
    display: none !important;
  }
  .tutor-img-list tutor-img-list-left {
    display: none !important;
  }
}

.newsletter-gradient-bg {
  background: linear-gradient(135deg, #0057b7 0%, #001b44 100%);
  border-radius: 1.7rem;
  padding: 2.5rem 1.5rem;
  display: flex;
  align-items: stretch;
  justify-content: center;
}

/* Remove white framing from sections */
.parents-zone-section,
.tutor-zone-section,
.pupil-zone-section,
.newsletter-zone-section {
  background: transparent;
  box-shadow: none;
  padding: 0;
}

/* Confidence block image 20% further right */
.confidence-img-right {
  position: relative;
  left: 20%;
  max-width: 100%;
}

/* WeAreTheFuture images 50% larger and 20% more central */
.big-pupil-img {
  width: 180px;
  left: 20%;
  transform: translateY(20%) !important;
}
.pupil-img-left {
  left: 20%;
}
.pupil-img-right {
  right: 20%;
}

/* Lego images 100% larger, 10% down, 10% more central */
.big-tutor-img {
  width: 216px;
  left: 10%;
  transform: translateY(10%) !important;
}
.tutor-img-left {
  left: 10%;
}
.tutor-img-right {
  right: 10%;
}

/* Question mark gap 30px, move 10% down */
.big-questionmark .question-curl {
  width: 96px;
}
.big-questionmark .question-period {
  width: 72px;
  margin-top: 30px;
}
.parent-img-outer {
  top: 65%;
}

@media (max-width: 900px) {
  .newsletter-gradient-bg {
    padding: 1.2rem 0.5rem;
  }
  .big-pupil-img {
    width: 90px;
    left: 50%;
    transform: translate(-50%, 20%) !important;
  }
  .big-tutor-img {
    width: 108px;
    left: 50%;
    transform: translate(-50%, 10%) !important;
  }
  .big-questionmark .question-curl {
    width: 54px;
  }
  .big-questionmark .question-period {
    width: 40px;
    margin-top: 15px;
  }
  .parent-img-outer {
    top: 80%;
  }
}

.zone-list-row {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  gap: 0;
  overflow: visible;
}
.parent-img-list,
.tutor-img-list,
.pupil-img-list {
  height: 150px;
  width: auto;
  display: block;
  flex-shrink: 0;
}
.parent-img-list-left,
.tutor-img-list-left,
.pupil-img-list-left {
  margin-right: 1.5rem;
}
.parent-img-list-right,
.tutor-img-list-right,
.pupil-img-list-right {
  margin-left: 1.5rem;
}
.parent-img-list-left-period {
  margin-right: 1.5rem;
  margin-top: 30px;
}
.parent-img-list-right-period {
  margin-left: 1.5rem;
  margin-top: 30px;
}
.tutor-list,
.parent-list,
.pupil-list {
  margin: 0 0;
}
@media (max-width: 900px) {
  .zone-list-row {
    flex-direction: column;
    align-items: center;
  }
  .parent-img-list-right,
  .parent-img-list-right-period,
  .tutor-img-list-right,
  .pupil-img-list-right {
    display: none !important;
  }
  .parent-img-list-left,
  .parent-img-list-left-period,
  .tutor-img-list-left,
  .pupil-img-list-left {
    margin: 0 auto 0.5rem auto;
    display: block;
  }
  .parent-img-list,
  .tutor-img-list,
  .pupil-img-list {
    height: 90px;
  }
}

.pupil-img-list {
  height: 248px; /* 216px * 1.15 = 248px */
}
.pupil-img-list-left {
  display: block !important;
  margin-right: 1.5rem;
  position: relative;
  top: 30px;
  left: 90px;
  z-index: 10;
}
.pupil-img-list-right {
  display: block !important;
  margin-left: 1.5rem;
  position: relative;
  top: 30px;
  right: 90px;
  z-index: 10;
}
@media (max-width: 900px) {
  .pupil-img-list {
    height: 150px;
  }

  /* Portrait orientation */

  /* Responsive navigation for smaller screens */
  @media (max-width: 900px) {
    .main-nav ul {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      width: 100%;
      padding: 0;
    }

    .main-nav ul li {
      border-left: none;
      border-bottom: 1px solid #add8e6;
      width: 100%;
      text-align: center;
    }

    .main-nav ul li a {
      padding: 12px 10px;
      font-size: 0.95em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* Portrait-specific adjustments */
    @media (orientation: portrait) {
      .main-nav ul {
        grid-template-columns: 1fr 1fr;
      }

      .main-nav ul li a {
        font-size: 0.9em;
        padding: 10px 5px;
      }
    }
  }

  @media (orientation: portrait) {
    .pupil-img-list-left {
      left: 0;
      top: 15px;
    }
    .pupil-img-list-right {
      right: 0;
      top: 15px;
    }
  }

  /* Portrait-specific adjustments for hero and CTA banners - MOVED TO contact-responsive-module.css */

  /* Responsive testimonials for smaller screens */
  @media (max-width: 900px) {
    .testimonials-laced {
      min-height: 500px; /* Increase height to accommodate stacked quotes */
    }

    .testimonial-quote-card {
      max-width: 85%;
      width: 85%;
      left: 0 !important;
      right: 0 !important;
      margin-left: auto;
      margin-right: auto;
    }

    /* Reposition quotes to prevent overlap */
    .testimonials-laced .testimonial-quote-card:nth-child(1) {
      top: 5% !important;
    }

    .testimonials-laced .testimonial-quote-card:nth-child(2) {
      top: 37% !important;
      right: 0 !important;
    }

    .testimonials-laced .testimonial-quote-card:nth-child(3) {
      top: 57% !important;
    }

    /* Dynamic testimonials now use standard dyn-block pattern - no special responsive rules needed */

    /* Portrait-specific adjustments */
    @media (orientation: portrait) {
      .testimonials-laced {
        min-height: 550px; /* Even more height for portrait */
      }

      .testimonial-quote-card {
        max-width: 90%;
        width: 90%;
        padding: 1rem 0.9rem;
        font-size: 1rem;
      }

      /* Further adjust positions for portrait */
      .testimonials-laced .testimonial-quote-card:nth-child(1) {
        top: 5% !important;
        font-size: 0.95rem !important;
      }

      .testimonials-laced .testimonial-quote-card:nth-child(2) {
        top: 33% !important; /* Moved up to prevent overlap */
        font-size: 0.95rem !important;
      }

      .testimonials-laced .testimonial-quote-card:nth-child(3) {
        top: 75% !important; /* Moved down to prevent overlap */
        font-size: 0.95rem !important;
      }
    }
  }

  /* Portrait-specific adjustments for nav - REMOVED TRANSFORM to align with header/banner */

  /* Portrait-specific border adjustments */
  @media (orientation: portrait) {
    .main-nav ul li:nth-child(odd) {
      border-right: 1px solid rgba(173, 216, 230, 0.3) !important;
    }
  }

  /* Landscape orientation - wider positioning */
  @media (orientation: landscape) {
    .pupil-img-list-left {
      left: 20px;
      top: 15px;
    }
    .pupil-img-list-right {
      right: 20px;
      top: 15px;
    }
  }
}

/* LEGO images: double size, move down 50px, more central by 100px, high z-index */
.tutor-img-list-left {
  height: 300px;
  margin-right: 1.5rem;
  position: relative;
  top: 50px;
  left: 100px;
  z-index: 20;
}
.tutor-img-list-right {
  height: 300px;
  margin-left: 1.5rem;
  position: relative;
  top: 50px;
  right: 100px;
  z-index: 20;
}

/* Question mark images: curl 50% larger, period 50% smaller, stack curl above period */
.parent-img-list-left,
.parent-img-list-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.parent-img-list-left .parent-img-list-left-period,
.parent-img-list-right .parent-img-list-right-period {
  margin-top: 0;
}
.parent-img-list-left .parent-img-list-left,
.parent-img-list-right .parent-img-list-right {
  height: 225px; /* 150px * 1.5 = 225px */
  width: auto;
  display: block;
}
.parent-img-list-left-period,
.parent-img-list-right-period {
  height: 37.5px; /* 75px * 0.5 = 37.5px */
  width: auto;
  display: block;
  margin-top: 0;
}
.parent-img-list-left,
.parent-img-list-right {
  margin: 0;
  position: relative;
  z-index: 10;
}

@media (max-width: 900px) {
  .tutor-img-list-left,
  .tutor-img-list-right {
    height: 150px;
    top: 25px;
  }

  /* Portrait orientation */
  @media (orientation: portrait) {
    .tutor-img-list-left {
      left: 0;
      top: 10px !important; /* Move up to avoid overlapping "Tutor Highlight" heading */
    }
    .tutor-img-list-right {
      right: 0;
    }
  }

  /* Landscape orientation - wider positioning */
  @media (orientation: landscape) {
    .tutor-img-list-left {
      left: 20px;
    }
    .tutor-img-list-right {
      right: 20px;
    }
  }
  .parent-img-list-left .parent-img-list-left,
  .parent-img-list-right .parent-img-list-right {
    height: 112px;
  }
  .parent-img-list-left-period,
  .parent-img-list-right-period {
    height: 19px;
  }
}

.parents-box {
  background: #b8d4ff;
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  text-align: center;
  padding: 1.5rem;
  max-width: 1100px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}
.questionmark-row {
  position: relative;
  width: 100%;
  height: 250px;
  margin-bottom: -60px;
}
.questionmark-stack {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center-align the items horizontally */
  justify-content: flex-start; /* Start from the top */
  position: absolute;
  top: 0;
  z-index: 30;
}
.parent-stack-left {
  left: -10px; /* Moved 10px to the left */
}
.parent-stack-right {
  right: -20px; /* Moved 20px to the right */
}
.parent-img-list-left,
.parent-img-list-right {
  height: 225px;
  width: auto;
  display: block;
}
.parent-img-list-left-period,
.parent-img-list-right-period {
  height: 37.5px;
  width: auto;
  display: block;
  margin-top: 5px; /* Small gap between curl and period */
  position: relative;
  left: 0; /* Ensure the period is centered under the curl */
  right: 0;
}
@media (max-width: 900px) and (orientation: portrait) {
  .parents-box {
    max-width: 98vw;
  }
  .questionmark-row {
    height: 140px;
  }
  .parent-stack-right {
    display: none;
  }
  .parent-stack-left {
    left: calc(50% - 10px); /* Centered but shifted 10px to the left */
    transform: translateX(-50%);
  }
  .parent-img-list-left {
    height: 112px;
  }
  .parent-img-list-left-period {
    height: 19px;
    left: -10px; /* Move 10px to the left */
    top: 0;
    position: relative;
    margin-top: 5px; /* Small gap between curl and period */
  }
}

/* Removed landscape-specific media query to hide right question mark in all restricted viewports */

/* Align the question mark curl's lowest point directly above the center of the period */
.parent-stack-left .parent-img-list-left-period {
  position: relative;
  left: -10px; /* Move 10px to the left */
  margin-top: 5px; /* Small gap between curl and period */
}
.parent-stack-right .parent-img-list-right-period {
  position: relative;
  right: 0; /* Center the period under the curl */
  margin-top: 5px; /* Small gap between curl and period */
}
.parent-stack-right .parent-img-list-right {
  right: 20px; /* Move 20px to the right */
}

@media (max-width: 900px) {
  .parents-box {
    max-width: 98vw;
  }
  .questionmark-row {
    height: 140px;
  }
  /* Hide right question mark in all orientations under 900px */
  .parent-stack-right {
    display: none;
  }
  /* Portrait-specific styles */
  @media (orientation: portrait) {
    .parent-stack-left {
      left: calc(50% - 10px); /* Centered but shifted 10px to the left */
      transform: translateX(-50%);
    }
  }
  /* Landscape-specific styles */
  @media (orientation: landscape) {
    .parent-stack-left {
      left: -10px; /* Keep the 10px left offset */
    }
  }
  .parent-img-list-left {
    height: 112px;
  }
  .parent-img-list-left-period {
    height: 19px;
    left: -10px; /* Move 10px to the left */
    top: 0;
    position: relative;
    margin-top: 5px; /* Small gap between curl and period */
  }
  /* Narrow floating footer for social links - full width */
  .site-footer .footer-icons {
    gap: 0.7rem;
    font-size: 1.5rem;
    flex-wrap: wrap;
    max-width: 100vw; /* Allow full viewport width */
  }
  .site-footer {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    left: 0; /* Start from left edge instead of -50px */
    width: 100vw; /* Ensure full viewport width */
  }
  /* Center legoRight.png below section in portrait mode only */
  @media (orientation: portrait) {
    .tutor-img-list-right {
      display: block !important;
      position: static;
      margin: 1.5rem auto 0 auto;
      left: 0;
      right: 0;
      top: 0;
      height: 150px;
    }
  }
  /* Move parent-box paragraph lower */
  .parents-box > p:first-of-type {
    margin-top: 60px;
  }
  /* About us section: stack columns, center content */
  .two-col-content {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 1.2rem !important;
  }
  .two-col-content > div {
    width: 100% !important;
    max-width: 100% !important;
  }
  .two-col-content img {
    margin: 0 auto 1rem auto !important;
    display: block !important;
  }
  /* Center the shield in the confidence section */
  .confidence-img-right {
    position: static !important;
    left: auto !important;
    margin: 0 auto !important;
    display: block !important;
    max-width: 150px !important;
  }
  /* Testimonials: move lowest lower, highest higher */
  .testimonials-laced .testimonial-quote-card:first-child {
    top: 2% !important;
  }
  .testimonials-laced .testimonial-quote-card:last-child {
    bottom: 2% !important;
  }
  /* Center mission statement and join button */
  .mission-statement {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  /* Landscape-specific adjustments for restricted viewports */
  @media (orientation: landscape) {
    /* Move mission statement h1 15px to the right */
    .mission-statement {
      transform: translateX(15px) !important;
    }

    /* Move STRIVE section 10px to the right */
    .strive-bg-section {
      transform: translateX(10px) !important;
    }

    /* CTA banner landscape adjustment - MOVED TO contact-responsive-module.css */
  }
  /* Header: position heading to the right with line breaks, button bottom right */
  header {
    flex-direction: row !important;
    align-items: flex-start !important;
    padding: 18px !important;
    position: relative !important;
    min-height: 120px !important;
  }
  header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    text-align: right !important;
    width: 100% !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
    padding-right: 10px !important;
  }
  .header-links {
    position: absolute !important;
    bottom: 10px !important;
    right: 10px !important;
    margin: 0 !important;
    z-index: 10 !important; /* Ensure buttons are above other elements */
    justify-content: flex-end !important; /* Ensure buttons are right-aligned */
  }
  /* Nav: stack links in two columns */
  .main-nav ul {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important;
    justify-items: center !important;
    width: 100% !important;
  }
  .main-nav ul li {
    width: 100% !important;
    text-align: center !important;
  }

  /* Landscape-specific nav adjustment - REMOVED TRANSFORM to align with header/banner */
}

/* Dynamic sections styling */
.dynamic-section-container {
  width: 100%;
  max-width: 1080px;
  margin: 2rem auto;
}

.dyn-block {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 2px 18px rgba(0, 87, 183, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
}

.dyn-block img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto 1.5rem auto;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Ensure two-column layout images maintain proper sizing (duplicate rule for specificity) */
.dyn-block.two-col-content img,
.two-col-content.dyn-block img {
  max-width: 782px !important; /* Increased by 77% from 442px (442 * 1.77 = 782.34) */
  border-radius: 1rem !important;
  transform-origin: left top; /* Expand left and downward */
}

.dyn-block h2 {
  color: #0057b7;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.dyn-content {
  line-height: 1.6;
}

.dyn-content p {
  margin-bottom: 1rem;
}

@media (max-width: 900px) {
  .dyn-block {
    padding: 1.5rem;
  }

  .dyn-block h2 {
    font-size: 1.5rem;
  }
}

/* Portrait-specific adjustments for dynamic sections alignment */
@media (max-width: 1200px) and (orientation: portrait) {
  #dynamicSections,
  #dynamicSectionsTop,
  #dynamicSectionsMiddle,
  #dynamicSectionsBottom {
    width: 83% !important; /* Further reduced to move standard sections more left - !important to override conflicting rules */
    margin-left: auto !important;
    margin-right: auto !important;
    transform: translateX(-7%) !important; /* Increased transform to shift further left */
  }

  /* Specific fix for team member sections only */
  #dynamicSections .strive-team-card,
  #dynamicSectionsTop .strive-team-card,
  #dynamicSectionsMiddle .strive-team-card,
  #dynamicSectionsBottom .strive-team-card,
  .dynamic-section-container:has(.team-grid) {
    width: 99% !important; /* Keep almost full width for team sections */
    margin-left: auto !important;
    margin-right: auto !important;
    transform: translateX(5%) !important; /* Reduced from -3% to shift team sections slightly right */
  }

  .dynamic-section-container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .dyn-block {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Landscape-specific adjustments for dynamic sections alignment */
@media (max-width: 1200px) and (orientation: landscape) {
  #dynamicSections,
  #dynamicSectionsTop,
  #dynamicSectionsMiddle,
  #dynamicSectionsBottom {
    width: 92%;
    margin-left: auto;
    margin-right: auto;
    transform: translateX(-7%); /* Shift more to the left to align with static content */
  }

  .dynamic-section-container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .dyn-block {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}

/* ======================================================================
   Mobile viewport hot-fix for full-bleed sections
   ====================================================================== */
@media (max-width: 1200px) and (orientation: portrait) {
  /*
   * The original 100vw width + –50vw margins push the section's
   * bounding box outside the visual viewport on narrow screens.
   * That prevents our IntersectionObserver from adding `is-visible`.
   * Constraining the block to normal flow fixes the problem.
   * Extended to 1200px to match restricted viewport definition in JS.
   */
  .strive-bg-section {
    width: 100%; /* use parent's natural width            */
    position: relative; /* cancel the earlier 'left: 50%' trick   */
    left: auto;
    right: auto;
    margin-left: auto; /* reset any negative margins             */
    margin-right: auto;
  }
}

/* ------------------------------
   Restricted portrait hot-fix
   ------------------------------ */
@media (max-width: 1200px) and (orientation: portrait) {
  /* 1️⃣  Keep the background full-width but cancel the old trickery */
  .strive-bg-section,
  .testimonials-bg-section {
    width: 100% !important; /* logical width = visual width - FORCE OVERRIDE */
    position: relative !important;
    left: auto !important; /* FORCE OVERRIDE of left: 50% */
    right: auto !important; /* FORCE OVERRIDE of right: 50% */
    margin-left: auto !important; /* FORCE OVERRIDE of margin-left: -50vw */
    margin-right: auto !important; /* FORCE OVERRIDE of margin-right: -50vw */

    /* keep horizontal safety-net               */
    overflow-x: hidden;
    /* ✅ let the section grow vertically       */
    overflow-y: visible;

    /* (optional) don't pin it to 800 px either */
    min-height: auto;
  }

  /* 2️⃣  The card now drives the fade-in, so keep it inside the viewport */
  .strive-overlay-card {
    max-width: 92vw; /* same optical margin you already use */
  }
  /* 💡 1. Drop the nested-flex that collapses - only for static team sections */
  .team-members[style*='display: flex'] {
    display: block !important; /* stack cards top-to-bottom        */
  }

  /* 💡 2. Let each card take the full line - only for static team sections */
  .team-members[style*='display: flex'] .team-member {
    flex: none !important; /* cancel 1 1 300px                 */
    width: 100%;
    max-width: 320px; /* keeps the desktop look           */
    margin: 0 auto 20px; /* nice centred column              */
  }

  /* 🎯 Center static testimonials in restricted viewports */
  main > .testimonials-bg-section {
    margin: 0 auto !important;
  }
}
