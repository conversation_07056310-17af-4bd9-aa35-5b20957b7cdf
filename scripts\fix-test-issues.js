#!/usr/bin/env node
/**
 * <PERSON>ript to fix test-specific linting issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing test-specific linting issues...');

// Fix unused variables in test files by prefixing with underscore
const testFixes = [
    {
        file: 'tests/e2e/hardware-specific.spec.js',
        fixes: [
            { from: 'parentStyle', to: '_parentStyle' },
            { from: 'links', to: '_links' },
        ],
    },
    {
        file: 'tests/integration/api/blog-system.test.js',
        fixes: [
            { from: 'content', to: '_content' },
            { from: 'newContent', to: '_newContent' },
        ],
    },
    {
        file: 'tests/integration/api/dynamic-sections.test.js',
        fixes: [{ from: 'vi', to: '_vi' }],
    },
    {
        file: 'tests/integration/api/image-upload.test.js',
        fixes: [{ from: 'expectedHeight', to: '_expectedHeight' }],
    },
    {
        file: 'tests/integration/api/login.test.js',
        fixes: [{ from: 'e', to: '_e' }],
    },
    {
        file: 'tests/integration/api/tutor-search.test.js',
        fixes: [
            { from: 'emptyQuery', to: '_emptyQuery' },
            { from: 'results', to: '_results' },
            { from: 'ratingValid', to: '_ratingValid' },
            { from: 'specialCharQuery', to: '_specialCharQuery' },
        ],
    },
    {
        file: 'tests/migration/schema-migration.test.js',
        fixes: [
            { from: 'existingSection', to: '_existingSection' },
            { from: 'listSection', to: '_listSection' },
            { from: 'testimonialSection', to: '_testimonialSection' },
        ],
    },
    {
        file: 'tests/unit/models/User.test.js',
        fixes: [{ from: 'mongoose', to: '_mongoose' }],
    },
];

testFixes.forEach(({ file, fixes }) => {
    const filePath = path.join(process.cwd(), file);

    if (!fs.existsSync(filePath)) {
        console.log(`⚠️  File not found: ${file}`);
        return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    fixes.forEach(({ from, to }) => {
        // Only replace variable declarations and assignments, not all occurrences
        const patterns = [
            new RegExp(`\\bconst\\s+${from}\\b`, 'g'),
            new RegExp(`\\blet\\s+${from}\\b`, 'g'),
            new RegExp(`\\bvar\\s+${from}\\b`, 'g'),
            new RegExp(`\\b${from}\\s*=`, 'g'),
            new RegExp(`catch\\s*\\(\\s*${from}\\s*\\)`, 'g'),
            new RegExp(`\\(\\s*${from}\\s*\\)\\s*=>`, 'g'),
        ];

        patterns.forEach(pattern => {
            if (pattern.test(content)) {
                content = content.replace(pattern, match => match.replace(from, to));
                modified = true;
            }
        });
    });

    if (modified) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ Fixed ${file}`);
    } else {
        console.log(`ℹ️  No changes needed in ${file}`);
    }
});

console.log('🎉 Test fixes completed!');
