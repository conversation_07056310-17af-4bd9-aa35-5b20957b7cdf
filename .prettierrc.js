// .prettierrc.js - Prettier configuration for tutorScotland project
module.exports = {
    // Basic formatting
    semi: true, // Always use semicolons
    singleQuote: true, // Use single quotes instead of double quotes
    quoteProps: 'as-needed', // Only quote object properties when needed
    trailingComma: 'es5', // Trailing commas where valid in ES5 (objects, arrays, etc.)

    // Indentation
    tabWidth: 4, // Use 4 spaces for indentation
    useTabs: false, // Use spaces instead of tabs

    // Line length and wrapping
    printWidth: 100, // Wrap lines at 100 characters
    bracketSpacing: true, // Spaces inside object literals: { foo: bar }
    bracketSameLine: false, // Put > on new line in JSX

    // Arrow functions
    arrowParens: 'avoid', // Avoid parentheses around single arrow function parameters

    // HTML/CSS (for any embedded styles)
    htmlWhitespaceSensitivity: 'css',

    // End of line
    endOfLine: 'lf', // Use LF line endings (Unix style)

    // Embedded language formatting
    embeddedLanguageFormatting: 'auto',

    // Override settings for specific file types
    overrides: [
        {
            files: '*.json',
            options: {
                tabWidth: 2, // Use 2 spaces for JSON files
                printWidth: 80, // Shorter lines for JSON
            },
        },
        {
            files: '*.md',
            options: {
                tabWidth: 2, // Use 2 spaces for Markdown
                printWidth: 80, // Shorter lines for better readability
                proseWrap: 'always',
            },
        },
        {
            files: '*.html',
            options: {
                tabWidth: 2, // Use 2 spaces for HTML
                printWidth: 120, // Allow longer lines for HTML
            },
        },
        {
            files: '*.css',
            options: {
                tabWidth: 2, // Use 2 spaces for CSS
                printWidth: 120, // Allow longer lines for CSS
            },
        },
        {
            files: ['package.json', 'package-lock.json'],
            options: {
                tabWidth: 2, // Use 2 spaces for package files
                printWidth: 120,
            },
        },
    ],
};
