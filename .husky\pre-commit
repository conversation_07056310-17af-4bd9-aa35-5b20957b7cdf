#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running code quality checks before commit..."

echo "📝 Checking code formatting..."
npm run format:check
if [ $? -ne 0 ]; then
    echo "❌ Code formatting issues found. Run 'npm run format' to fix them."
    exit 1
fi

echo "🔧 Running ESLint..."
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ Linting issues found. Run 'npm run lint:fix' to fix auto-fixable issues."
    exit 1
fi

echo "🧪 Running unit tests..."
npm run test:unit
if [ $? -ne 0 ]; then
    echo "❌ Unit tests failed."
    exit 1
fi

echo "✅ All pre-commit checks passed!"
