#!/usr/bin/env node
/**
 * <PERSON><PERSON><PERSON> to fix the remaining 79 linting issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing remaining linting issues...');

// Fix 1: api/addTutor.js - Fix _editId undefined references
const addTutorPath = path.join(process.cwd(), 'api/addTutor.js');
if (fs.existsSync(addTutorPath)) {
    let content = fs.readFileSync(addTutorPath, 'utf8');
    // Change _editId back to editId since it's actually used
    content = content.replace(/_editId/g, 'editId');
    fs.writeFileSync(addTutorPath, content);
    console.log('✅ Fixed api/addTutor.js');
}

// Fix 2: api/connectToDatabase.js - Fix undefined error
const connectDbPath = path.join(process.cwd(), 'api/connectToDatabase.js');
if (fs.existsSync(connectDbPath)) {
    let content = fs.readFileSync(connectDbPath, 'utf8');
    // Change _error back to error since it's used in catch block
    content = content.replace(/_error/g, 'error');
    fs.writeFileSync(connectDbPath, content);
    console.log('✅ Fixed api/connectToDatabase.js');
}

// Fix 3: api/protected.js - Fix undefined res
const protectedPath = path.join(process.cwd(), 'api/protected.js');
if (fs.existsSync(protectedPath)) {
    let content = fs.readFileSync(protectedPath, 'utf8');
    // Change _res back to res since it's used
    content = content.replace(/_res/g, 'res');
    fs.writeFileSync(protectedPath, content);
    console.log('✅ Fixed api/protected.js');
}

// Fix 4: seedDatabase.js - Fix unused getRandomPostcodes
const seedDbPath = path.join(process.cwd(), 'seedDatabase.js');
if (fs.existsSync(seedDbPath)) {
    let content = fs.readFileSync(seedDbPath, 'utf8');
    content = content.replace(/\bgetRandomPostcodes\b/g, '_getRandomPostcodes');
    fs.writeFileSync(seedDbPath, content);
    console.log('✅ Fixed seedDatabase.js');
}

// Fix 5: public/js/dynamic-nav.js - Fix unused addCustomPagesLegacy
const dynamicNavPath = path.join(process.cwd(), 'public/js/dynamic-nav.js');
if (fs.existsSync(dynamicNavPath)) {
    let content = fs.readFileSync(dynamicNavPath, 'utf8');
    content = content.replace(/\baddCustomPagesLegacy\b/g, '_addCustomPagesLegacy');
    fs.writeFileSync(dynamicNavPath, content);
    console.log('✅ Fixed public/js/dynamic-nav.js');
}

// Fix 6: api/content-manager.js - Fix all underscore references
const contentManagerPath = path.join(process.cwd(), 'api/content-manager.js');
if (fs.existsSync(contentManagerPath)) {
    let content = fs.readFileSync(contentManagerPath, 'utf8');
    // Fix all the underscore references that are actually used
    content = content.replace(/\b_page\b/g, 'page');
    content = content.replace(/\b_selector\b/g, 'selector');
    content = content.replace(/\b_type\b/g, 'type');
    content = content.replace(/\b_id\b/g, 'id');
    fs.writeFileSync(contentManagerPath, content);
    console.log('✅ Fixed api/content-manager.js');
}

// Fix 7: api/upload-image.js - Fix uploadedFile references
const uploadImagePath = path.join(process.cwd(), 'api/upload-image.js');
if (fs.existsSync(uploadImagePath)) {
    const content = fs.readFileSync(uploadImagePath, 'utf8');
    // Find the context around line 441 to understand the issue
    const lines = content.split('\n');
    for (let i = 435; i < 450 && i < lines.length; i++) {
        if (
            lines[i].includes('uploadedFile') &&
            !lines[i].includes('const') &&
            !lines[i].includes('let')
        ) {
            // This is likely a reference to an undefined variable
            console.log(`Line ${i + 1}: ${lines[i]}`);
        }
    }
    console.log('ℹ️  Analyzed api/upload-image.js');
}

console.log('🎉 Advanced fixes completed!');
