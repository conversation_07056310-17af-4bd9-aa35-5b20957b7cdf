#!/usr/bin/env node
/**
 * <PERSON>ript to fix the final remaining linting issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing final linting issues...');

// Fix 1: api/addTutor.js - unused editId
const addTutorPath = path.join(process.cwd(), 'api/addTutor.js');
if (fs.existsSync(addTutorPath)) {
    let content = fs.readFileSync(addTutorPath, 'utf8');
    content = content.replace(/\beditId\b/g, '_editId');
    fs.writeFileSync(addTutorPath, content);
    console.log('✅ Fixed api/addTutor.js');
}

// Fix 2: api/connectToDatabase.js - unused error
const connectDbPath = path.join(process.cwd(), 'api/connectToDatabase.js');
if (fs.existsSync(connectDbPath)) {
    let content = fs.readFileSync(connectDbPath, 'utf8');
    content = content.replace(/catch\s*\(\s*error\s*\)/g, 'catch (_error)');
    fs.writeFileSync(connectDbPath, content);
    console.log('✅ Fixed api/connectToDatabase.js');
}

// Fix 3: api/content-manager.js - unused variables
const contentManagerPath = path.join(process.cwd(), 'api/content-manager.js');
if (fs.existsSync(contentManagerPath)) {
    let content = fs.readFileSync(contentManagerPath, 'utf8');
    // Fix the destructuring to use underscores for unused vars
    content = content.replace(
        'const { page, selector, type, id } = query;',
        'const { page: _page, selector: _selector, type: _type, id: _id } = query;'
    );
    // Fix line 325 similar issue
    content = content.replace(
        /const\s*{\s*page,\s*selector\s*}\s*=\s*req\.query;/g,
        'const { page: _page, selector: _selector } = req.query;'
    );
    fs.writeFileSync(contentManagerPath, content);
    console.log('✅ Fixed api/content-manager.js');
}

// Fix 4: api/protected.js - unused res
const protectedPath = path.join(process.cwd(), 'api/protected.js');
if (fs.existsSync(protectedPath)) {
    let content = fs.readFileSync(protectedPath, 'utf8');
    content = content.replace(/\bres\b/g, '_res');
    fs.writeFileSync(protectedPath, content);
    console.log('✅ Fixed api/protected.js');
}

// Fix 5: public/js/dynamic-sections.js - undefined index
const dynamicSectionsPath = path.join(process.cwd(), 'public/js/dynamic-sections.js');
if (fs.existsSync(dynamicSectionsPath)) {
    let content = fs.readFileSync(dynamicSectionsPath, 'utf8');
    // Find the line with undefined index and fix it
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('index') && i > 600 && i < 610) {
            // This is likely the problematic line - replace with a proper index
            lines[i] = lines[i].replace(/\bindex\b/g, 'i');
        }
    }
    content = lines.join('\n');
    fs.writeFileSync(dynamicSectionsPath, content);
    console.log('✅ Fixed public/js/dynamic-sections.js');
}

// Fix 6: public/js/visual-editor-v2.js - unused error and duplicate methods
const visualEditorPath = path.join(process.cwd(), 'public/js/visual-editor-v2.js');
if (fs.existsSync(visualEditorPath)) {
    let content = fs.readFileSync(visualEditorPath, 'utf8');
    content = content.replace(/catch\s*\(\s*error\s*\)/g, 'catch (_error)');

    // Remove duplicate methods by finding and removing the second occurrence
    const lines = content.split('\n');
    const methodsToRemove = ['handleRestore', 'handleUpload'];

    methodsToRemove.forEach(methodName => {
        let foundFirst = false;
        let inMethod = false;
        let braceCount = 0;

        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(`${methodName}(`)) {
                if (!foundFirst) {
                    foundFirst = true;
                } else {
                    // This is the duplicate - mark for removal
                    inMethod = true;
                    braceCount = 0;
                    lines[i] = ''; // Remove the method declaration
                }
            }

            if (inMethod) {
                // Count braces to find method end
                const openBraces = (lines[i].match(/{/g) || []).length;
                const closeBraces = (lines[i].match(/}/g) || []).length;
                braceCount += openBraces - closeBraces;

                lines[i] = ''; // Remove this line

                if (braceCount <= 0) {
                    inMethod = false;
                }
            }
        }
    });

    content = lines.join('\n');
    fs.writeFileSync(visualEditorPath, content);
    console.log('✅ Fixed public/js/visual-editor-v2.js');
}

console.log('🎉 Final fixes completed!');
