// models/user.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: String,
    email: { type: String, unique: true },
    password: String,
    // Now includes 'tutor' in the enum
    role: { type: String, enum: ['parent', 'admin', 'tutor', 'blogwriter'], required: true },
});

// ===================================================================
// DATABASE INDEXES FOR OPTIMAL USER AUTHENTICATION
// ===================================================================

// Note: email index is automatically created due to unique: true in schema

// 1. Role-based queries for admin functions
userSchema.index({ role: 1 });

// 2. Compound index for role-based user management
userSchema.index({ role: 1, email: 1 });

module.exports = mongoose.models.User || mongoose.model('User', userSchema);
