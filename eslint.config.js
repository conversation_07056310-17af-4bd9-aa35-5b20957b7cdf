// eslint.config.js - ESLint configuration for tutorScotland project
import js from '@eslint/js';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';

export default [
    // Base JavaScript recommended rules
    js.configs.recommended,

    // Prettier integration
    prettierConfig,

    {
        files: ['**/*.js'],
        plugins: {
            prettier,
        },
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: 'module',
            globals: {
                // Node.js globals
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                module: 'readonly',
                require: 'readonly',
                exports: 'readonly',
                global: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',

                // Browser globals for frontend files
                window: 'readonly',
                document: 'readonly',
                navigator: 'readonly',
                fetch: 'readonly',
                localStorage: 'readonly',
                sessionStorage: 'readonly',
                alert: 'readonly',
                confirm: 'readonly',
                URL: 'readonly',
                location: 'readonly',
                history: 'readonly',
                CustomEvent: 'readonly',
                IntersectionObserver: 'readonly',
                MutationObserver: 'readonly',

                // Testing globals
                describe: 'readonly',
                it: 'readonly',
                test: 'readonly',
                expect: 'readonly',
                beforeEach: 'readonly',
                afterEach: 'readonly',
                beforeAll: 'readonly',
                afterAll: 'readonly',
                vi: 'readonly',

                // Test-specific globals
                request: 'readonly',
                app: 'readonly',
                testSectionModel: 'readonly',
                results: 'readonly',
            },
        },
        rules: {
            // Prettier integration
            'prettier/prettier': 'error',

            // Error prevention
            'no-unused-vars': [
                'error',
                {
                    argsIgnorePattern: '^_',
                    varsIgnorePattern: '^_',
                    caughtErrorsIgnorePattern: '^_',
                },
            ],
            'no-console': 'off', // Allow console.log for debugging
            'no-debugger': 'error',
            'no-alert': 'warn',

            // Best practices
            eqeqeq: ['error', 'always'],
            curly: ['error', 'all'],
            'no-eval': 'error',
            'no-implied-eval': 'error',
            'no-new-func': 'error',
            'no-script-url': 'error',

            // Code style (handled by Prettier, but some logical rules)
            'no-multiple-empty-lines': ['error', { max: 2, maxEOF: 1 }],
            'no-trailing-spaces': 'error',
            'eol-last': 'error',

            // Modern JavaScript
            'prefer-const': 'error',
            'no-var': 'error',
            'prefer-arrow-callback': 'warn',
            'prefer-template': 'warn',

            // Error handling
            'no-throw-literal': 'error',
            'prefer-promise-reject-errors': 'error',

            // Security
            'no-new-wrappers': 'error',
            'no-caller': 'error',
            'no-extend-native': 'error',
        },
    },

    // Specific overrides for different file types
    {
        files: ['api/**/*.js'],
        languageOptions: {
            sourceType: 'commonjs', // API files use CommonJS
            globals: {
                // Node.js specific globals for API files
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                module: 'readonly',
                require: 'readonly',
                exports: 'readonly',
                global: 'readonly',
            },
        },
        rules: {
            // Allow require() in API files
            'no-undef': 'error',
        },
    },

    {
        files: ['public/js/**/*.js'],
        languageOptions: {
            sourceType: 'module', // Frontend files use ES modules
            globals: {
                // Browser-specific globals
                window: 'readonly',
                document: 'readonly',
                navigator: 'readonly',
                fetch: 'readonly',
                localStorage: 'readonly',
                sessionStorage: 'readonly',
                alert: 'readonly',
                confirm: 'readonly',
                console: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',
                URL: 'readonly',
                location: 'readonly',
                history: 'readonly',
                CustomEvent: 'readonly',
                IntersectionObserver: 'readonly',
                MutationObserver: 'readonly',
                // Global functions that may be defined in other scripts
                safeImg: 'readonly',
                initRollingBanner: 'readonly',
                loadDynamicSections: 'readonly',
                addCustomPagesLegacy: 'readonly',
                // Additional browser APIs
                FormData: 'readonly',
                URLSearchParams: 'readonly',
                XMLHttpRequest: 'readonly',
                createImageBitmap: 'readonly',
                OffscreenCanvas: 'readonly',
                File: 'readonly',
                AbortSignal: 'readonly',
                crypto: 'readonly',
                Element: 'readonly',
                getComputedStyle: 'readonly',
                self: 'readonly',
            },
        },
    },

    {
        files: ['tests/**/*.js', '**/*.test.js', '**/*.spec.js'],
        languageOptions: {
            globals: {
                // Testing framework globals
                describe: 'readonly',
                it: 'readonly',
                test: 'readonly',
                expect: 'readonly',
                beforeEach: 'readonly',
                afterEach: 'readonly',
                beforeAll: 'readonly',
                afterAll: 'readonly',
                vi: 'readonly',

                // Node.js globals for test files
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                module: 'readonly',
                require: 'readonly',
                exports: 'readonly',
                global: 'readonly',
            },
        },
        rules: {
            // Relax some rules for test files
            'no-unused-expressions': 'off',
            'max-lines-per-function': 'off',
        },
    },

    // Special handling for legacy script files
    {
        files: ['public/js/rolling-banner.js', 'public/responsive-helper.js'],
        languageOptions: {
            sourceType: 'script', // These are legacy script files
            ecmaVersion: 2015, // Allow const/let but not modules
            globals: {
                // Browser-specific globals
                window: 'readonly',
                document: 'readonly',
                navigator: 'readonly',
                fetch: 'readonly',
                localStorage: 'readonly',
                sessionStorage: 'readonly',
                alert: 'readonly',
                confirm: 'readonly',
                console: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',
                URL: 'readonly',
                location: 'readonly',
                history: 'readonly',
                CustomEvent: 'readonly',
                IntersectionObserver: 'readonly',
                MutationObserver: 'readonly',
            },
        },
    },

    // Files to ignore
    {
        ignores: [
            'node_modules/**',
            'coverage/**',
            'playwright-report/**',
            'test-results/**',
            '.vs/**',
            'obj/**',
            'bin/**',
            '*.min.js',
            'public/js/vendor/**',
        ],
    },
];
