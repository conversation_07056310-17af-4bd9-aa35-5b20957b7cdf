<!doctype html>
<html lang="en">
  <head>
    <!-- ====================================================================== -->
    <!--                          BLOG WRITER METADATA                         -->
    <!-- ====================================================================== -->

    <meta charset="UTF-8" />
    <title>Blog Writer – Tutors Alliance Scotland</title>

    <!-- ====================================================================== -->
    <!--                          STYLESHEETS & ASSETS                         -->
    <!-- ====================================================================== -->

    <!-- assets -->
    <link rel="icon" href="/images/bannerShield2.png" type="image/png" />
    <link rel="stylesheet" href="/styles2.css" />
    <link rel="stylesheet" href="/css/footer-module.css" />
    <link rel="stylesheet" href="/css/button-module.css" />
    <link rel="stylesheet" href="/css/typography-module.css" />
    <link rel="stylesheet" href="/css/animation-module.css" />
    <link rel="stylesheet" href="/header-banner.css" />
    <link rel="stylesheet" href="/css/nav.css" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <script src="/responsive-helper.js" defer=""></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>

    <!-- ====================================================================== -->
    <!--                          SEO & SOCIAL METADATA                        -->
    <!-- ====================================================================== -->

    <!--
        Blog Writer SEO Configuration

        @description Comprehensive metadata for blog management interface
        @seo Optimized for search engines with relevant keywords
        @social Open Graph tags for social media sharing
        @structured JSON-LD microdata for rich snippets
    -->

    <!-- SEO metadata -->
    <meta
      name="description"
      content="Tutors Alliance Scotland blog management system - create and manage educational blog posts for tutors and parents in Scotland."
    />
    <meta name="keywords" content="tutoring, Scotland, education, blog, tutors alliance" />
    <meta name="author" content="Tutors Alliance Scotland" />

    <!-- Open Graph metadata for social sharing -->
    <meta property="og:title" content="Blog Writer - Tutors Alliance Scotland" />
    <meta
      property="og:description"
      content="Create and manage educational blog posts for tutors and parents in Scotland."
    />
    <meta property="og:image" content="https://tutorsalliancescotland.co.uk/images/bannerShield2.png" />
    <meta property="og:url" content="https://tutorsalliancescotland.co.uk/blogWriter.html" />
    <meta property="og:type" content="website" />

    <!-- JSON‑LD placeholder (filled on submit) -->
    <script id="blogMicrodata" type="application/ld+json"></script>

    <!-- ====================================================================== -->
    <!--                          AUTHENTICATION GUARD                         -->
    <!-- ====================================================================== -->

    <!--
        Blog Writer Authentication System

        @description Protects blog writer interface from unauthorized access
        @role Requires 'blogwriter' role authentication
        @behavior Redirects to login page if authentication fails
        @api-endpoint /api/protected?role=blogwriter
        @security Uses HTTP-only cookies for session management
    -->

    <!-- blog‑writer guard -->
    <script>
      (async () => {
        try {
          const ok = await fetch('/api/protected?role=blogwriter', {
            credentials: 'include', // Important: include cookies for authentication
          });
          if (!ok.ok) {
            console.error('Authentication failed:', await ok.text());
            throw new Error('not writer');
          }
        } catch (error) {
          console.error('Authentication error:', error);
          location.href = '/login.html?role=blogwriter';
        }
      })();
    </script>

    <style>
      .wide-input {
        width: 100%; /* full width of the lilac panel                */
        padding: 12px 14px; /* taller & roomier                              */
        font-size: 1.15rem; /* larger text – tweak to taste (1rem ≈ 16 px)   */
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box; /* keeps the width from overflowing          */
      }

      /* Tab styling */
      .blog-tabs {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 2px solid #0057b7;
      }

      .tab-btn {
        padding: 10px 20px;
        background-color: #f0f0f0;
        border: none;
        border-radius: 5px 5px 0 0;
        cursor: pointer;
        margin-right: 5px;
        font-weight: bold;
      }

      .tab-btn.active {
        background-color: #0057b7;
        color: white;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      /* Blog list styling */
      .blog-list-container {
        margin-top: 20px;
        max-height: 500px;
        overflow-y: auto;
      }

      #blogListTable {
        width: 100%;
        border-collapse: collapse;
      }

      #blogListTable th,
      #blogListTable td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
      }

      #blogListTable th {
        background-color: #f2f2f2;
        position: sticky;
        top: 0;
      }

      .blog-filter {
        margin-bottom: 20px;
      }

      .loading-message {
        text-align: center;
        padding: 20px;
        color: #666;
      }

      .action-btn {
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 5px;
        font-size: 14px;
      }

      .edit-btn {
        background-color: #007bff;
        color: white;
      }

      .edit-btn:hover {
        background-color: #0056b3;
      }

      .delete-btn {
        background-color: #ff4d4d;
        color: white;
      }

      .delete-btn:hover {
        background-color: #ff0000;
      }

      .cancel-btn {
        background-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
      }

      .cancel-btn:hover {
        background-color: #545b62;
      }

      #currentImagePreview {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f8f9fa;
      }

      #currentImagePreview img {
        max-width: 150px;
        border-radius: 4px;
        margin-bottom: 10px;
      }

      /* Enhanced Blog form styling */
      .enhanced-blog-form {
        max-width: 1000px;
        margin: 0 auto;
      }

      .form-section {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 25px;
        padding: 20px;
        background-color: #fafafa;
      }

      .form-section legend {
        font-weight: bold;
        color: #0057b7;
        font-size: 1.1em;
        padding: 0 10px;
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
      }

      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group.full-width {
        flex: 1 1 100%;
      }

      .form-group.half-width {
        flex: 1 1 calc(50% - 10px);
      }

      .form-group.third-width {
        flex: 1 1 calc(33.333% - 14px);
      }

      .form-group label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
      }

      .wide-input,
      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
        box-sizing: border-box;
      }

      .wide-input:focus,
      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #0057b7;
        box-shadow: 0 0 0 3px rgba(0, 87, 183, 0.1);
      }

      .form-group textarea {
        resize: vertical;
        min-height: 60px;
      }

      .char-counter {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
        text-align: right;
      }

      .checkbox-label {
        display: flex !important;
        flex-direction: row !important;
        align-items: center;
        cursor: pointer;
        font-weight: normal !important;
      }

      .checkbox-label input[type='checkbox'] {
        width: auto !important;
        margin-right: 8px;
        margin-bottom: 0 !important;
      }

      .file-input {
        padding: 8px !important;
        background-color: white;
      }

      .current-image-preview {
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #dee2e6;
      }

      .current-image-preview img {
        max-width: 200px;
        max-height: 200px;
        border-radius: 4px;
        margin-bottom: 10px;
      }

      .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-start;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 2px solid #e0e0e0;
      }

      .btn-primary,
      .btn-secondary {
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .btn-primary {
        background-color: #0057b7;
        color: white;
      }

      .btn-primary:hover {
        background-color: #003d82;
        transform: translateY(-1px);
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background-color: #5a6268;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .form-group.half-width,
        .form-group.third-width {
          flex: 1 1 100%;
        }

        .form-row {
          gap: 10px;
        }

        .form-section {
          padding: 15px;
        }
      }
    </style>
  </head>
  <body>
    <!-- ====================================================================== -->
    <!--                          BLOG WRITER INTERFACE                        -->
    <!-- ====================================================================== -->

    <!--
        Blog Writer HTML Structure

        @description Complete blog management interface for content creators
        @sections
          - Header/Navigation: Site branding and navigation links
          - Blog Form: Comprehensive form for creating/editing blog posts
          - Blog List: Table displaying existing blog posts with edit/delete actions
        @features
          - Rich text editing with image upload support
          - SEO metadata fields (title, description, keywords)
          - Category and tag management
          - Draft/publish workflow
        @dependencies upload-helper.js for image handling
        @api-endpoints /api/blog-writer for CRUD operations
    -->

    <!-- HEADER / NAV identical to other pages -->
    <header>
      <h1 data-ve-block-id="df1818f1-65ca-4f6a-96c5-f1a8570c778c">Tutors Alliance Scotland</h1>
      <div class="header-links">
        <a class="banner-login-link login-box" href="/" data-ve-block-id="106101f5-9626-42a7-a2ad-f7d6b4570d70">Home</a>
        <a
          class="banner-login-link login-box"
          href="/login.html?role=admin"
          data-ve-block-id="9ddfc533-4f00-4698-a881-0d5ac2668385"
          >Login</a
        >
      </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <div class="rolling-banner">
      <div class="rolling-content" id="tutorBanner"></div>
    </div>

    <!-- ── BLOG MANAGEMENT INTERFACE ───────────────────────── -->
    <main>
      <!-- Tab navigation -->
      <div class="blog-tabs">
        <button id="createTabBtn" class="tab-btn active">Create Blog</button>
        <button id="manageTabBtn" class="tab-btn">Manage Blogs</button>
      </div>

      <!-- Create Blog Section -->
      <section id="newBlogSection" class="tab-content active">
        <h2 id="formHeading" data-ve-block-id="3abe36c0-caac-4c55-a567-31ba5ca6d3fe">Create a New Blog Post</h2>

        <form id="blogForm" class="enhanced-blog-form">
          <!-- Basic Information Section -->
          <fieldset class="form-section">
            <legend>Basic Information</legend>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="titleField">Title *</label>
                <input
                  id="titleField"
                  class="wide-input"
                  name="title"
                  required
                  placeholder="Enter your blog post title"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group half-width">
                <label for="authorField">Author *</label>
                <input id="authorField" class="wide-input" name="author" required placeholder="Author name" />
              </div>
              <div class="form-group half-width">
                <label for="slugField">URL Slug</label>
                <input
                  id="slugField"
                  class="wide-input"
                  name="slug"
                  placeholder="url-friendly-title (auto-generated if empty)"
                />
              </div>
            </div>
          </fieldset>

          <!-- Content Section -->
          <fieldset class="form-section">
            <legend>Content</legend>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="excerptField">Excerpt (Short Summary) *</label>
                <textarea
                  id="excerptField"
                  rows="3"
                  maxlength="200"
                  placeholder="Brief summary of your blog post (max 200 characters)"
                  required
                ></textarea>
                <small class="char-counter">0/200 characters</small>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group full-width">
                <label for="contentField">Main Content *</label>
                <textarea
                  id="contentField"
                  rows="15"
                  required
                  placeholder="Write your blog post content here..."
                ></textarea>
              </div>
            </div>
          </fieldset>

          <!-- SEO & Metadata Section -->
          <fieldset class="form-section">
            <legend>SEO & Metadata</legend>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="metaDescriptionField">Meta Description</label>
                <textarea
                  id="metaDescriptionField"
                  rows="2"
                  maxlength="160"
                  placeholder="SEO description for search engines (max 160 characters)"
                ></textarea>
                <small class="char-counter">0/160 characters</small>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group half-width">
                <label for="focusKeywordField">Focus Keyword</label>
                <input
                  id="focusKeywordField"
                  class="wide-input"
                  name="focusKeyword"
                  placeholder="Primary SEO keyword"
                />
              </div>
              <div class="form-group half-width">
                <label for="tagsField">Tags</label>
                <input id="tagsField" class="wide-input" name="tags" placeholder="tag1, tag2, tag3 (comma-separated)" />
              </div>
            </div>
          </fieldset>

          <!-- Publishing Options Section -->
          <fieldset class="form-section">
            <legend>Publishing Options</legend>
            <div class="form-row">
              <div class="form-group third-width">
                <label for="categoryField">Category</label>
                <select id="categoryField" name="category">
                  <option value="general">General</option>
                  <option value="parent">Parent</option>
                  <option value="tutor">Tutor</option>
                </select>
              </div>
              <div class="form-group third-width">
                <label for="statusField">Status</label>
                <select id="statusField" name="status">
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
              </div>
              <div class="form-group third-width">
                <label for="publishDateField">Publish Date/Time</label>
                <input type="datetime-local" id="publishDateField" name="publishDate" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="featuredField" name="featured" />
                  <span class="checkmark"></span>
                  Featured Post
                </label>
              </div>
            </div>
          </fieldset>

          <!-- Media Section -->
          <fieldset class="form-section">
            <legend>Media</legend>
            <div class="form-row">
              <div class="form-group full-width">
                <label for="imageField">Featured Image</label>
                <input type="file" id="imageField" accept="image/*" class="file-input" />
              </div>
            </div>

            <!-- Current image preview (for editing) -->
            <div id="currentImagePreview" style="display: none" class="current-image-preview">
              <p>Current Image:</p>
              <img src="" alt="Current blog image" />
              <label class="checkbox-label">
                <input type="checkbox" id="removeImageCheckbox" />
                <span class="checkmark"></span>
                Remove Current Image
              </label>
            </div>
          </fieldset>

          <!-- Form Actions -->
          <div class="form-actions">
            <button type="submit" id="submitBtn" class="btn-primary">Create Blog</button>
            <button type="button" id="cancelEditBtn" class="btn-secondary" style="display: none">Cancel Edit</button>
          </div>
        </form>
      </section>

      <!-- Manage Blogs Section -->
      <section id="manageBlogSection" class="tab-content">
        <h2 data-ve-block-id="6a6d76c0-4eaf-41cc-a1c1-9da7e14a3de2">Manage Existing Blog Posts</h2>

        <div class="blog-filter">
          <label for="blogCategoryFilter">Filter by category:</label>
          <select id="blogCategoryFilter">
            <option value="all">All Categories</option>
            <option value="general">General</option>
            <option value="parent">Parent</option>
            <option value="tutor">Tutor</option>
          </select>
          <button id="refreshBlogsBtn">Refresh List</button>
          <button
            id="migrateBlogsBtn"
            style="
              background-color: #28a745;
              color: white;
              margin-left: 10px;
              padding: 8px 15px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            "
          >
            Update Old Blogs
          </button>
        </div>

        <div class="blog-list-container">
          <table id="blogListTable">
            <thead>
              <tr>
                <th>Title</th>
                <th>Author</th>
                <th>Category</th>
                <th>Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="blogListBody">
              <!-- Blog entries will be loaded here -->
              <tr>
                <td colspan="5" class="loading-message">Loading blog posts...</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>
    </main>

    <!-- Hidden JSON-LD for SEO -->
    <script type="application/ld+json" id="blogMicrodata"></script>

    <!-- Load rolling banner script -->
    <script src="/responsive-helper.js" defer></script>
    <script src="/js/rolling-banner.js" defer></script>

    <!-- JS as module so we can import upload‑helper -->
    <script type="module">
      import { uploadImage } from '/js/upload-helper.js';

      /* Tab switching functionality */
      const createTabBtn = document.getElementById('createTabBtn');
      const manageTabBtn = document.getElementById('manageTabBtn');
      const newBlogSection = document.getElementById('newBlogSection');
      const manageBlogSection = document.getElementById('manageBlogSection');

      createTabBtn.addEventListener('click', () => {
        createTabBtn.classList.add('active');
        manageTabBtn.classList.remove('active');
        newBlogSection.classList.add('active');
        manageBlogSection.classList.remove('active');

        // Only reset form when switching to create tab if we're not in edit mode
        if (!blogForm.dataset.editId) {
          resetBlogForm();
        }
      });

      manageTabBtn.addEventListener('click', () => {
        manageTabBtn.classList.add('active');
        createTabBtn.classList.remove('active');
        manageBlogSection.classList.add('active');
        newBlogSection.classList.remove('active');
        loadBlogs(); // Load blogs when tab is clicked
      });

      // Get form elements
      const blogForm = document.getElementById('blogForm');
      const formHeading = document.getElementById('formHeading');
      const submitBtn = document.getElementById('submitBtn');
      const cancelEditBtn = document.getElementById('cancelEditBtn');
      const currentImagePreview = document.getElementById('currentImagePreview');
      const removeImageCheckbox = document.getElementById('removeImageCheckbox');

      // Form field references
      const titleField = document.getElementById('titleField');
      const authorField = document.getElementById('authorField');
      const slugField = document.getElementById('slugField');
      const categoryField = document.getElementById('categoryField');
      const statusField = document.getElementById('statusField');
      const excerptField = document.getElementById('excerptField');
      const metaDescriptionField = document.getElementById('metaDescriptionField');
      const focusKeywordField = document.getElementById('focusKeywordField');
      const tagsField = document.getElementById('tagsField');
      const featuredField = document.getElementById('featuredField');
      const publishDateField = document.getElementById('publishDateField');
      const contentField = document.getElementById('contentField');
      const imageField = document.getElementById('imageField');
      const blogMicrodata = document.getElementById('blogMicrodata');

      // Character counters
      function updateCharCounter(field, maxLength) {
        const counter = field.parentElement.querySelector('.char-counter');
        if (counter) {
          const currentLength = field.value.length;
          counter.textContent = `${currentLength}/${maxLength} characters`;
          counter.style.color = currentLength > maxLength ? '#dc3545' : '#666';
        }
      }

      // Add character counting for excerpt and meta description
      excerptField.addEventListener('input', () => updateCharCounter(excerptField, 200));
      metaDescriptionField.addEventListener('input', () => updateCharCounter(metaDescriptionField, 160));

      // Auto-generate slug from title
      titleField.addEventListener('input', () => {
        if (!slugField.value || slugField.dataset.autoGenerated === 'true') {
          const slug = titleField.value
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          slugField.value = slug;
          slugField.dataset.autoGenerated = 'true';
        }
      });

      // Mark slug as manually edited when user types in it
      slugField.addEventListener('input', () => {
        slugField.dataset.autoGenerated = 'false';
      });

      // Store all blogs for editing
      let allBlogs = [];

      /* Helper: Reset form to create mode */
      function resetBlogForm() {
        blogForm.reset();
        delete blogForm.dataset.editId;
        formHeading.textContent = 'Create a New Blog Post';
        submitBtn.textContent = 'Create Blog';
        cancelEditBtn.style.display = 'none';
        currentImagePreview.style.display = 'none';
        removeImageCheckbox.checked = false;
        blogMicrodata.textContent = '';

        // Reset character counters
        updateCharCounter(excerptField, 200);
        updateCharCounter(metaDescriptionField, 160);

        // Reset slug auto-generation flag
        slugField.dataset.autoGenerated = 'true';
      }

      /* Helper: Populate form for editing */
      function populateBlogForm(blog) {
        console.log('Populating form with blog data:', blog);

        // Basic Information fields
        titleField.value = blog.title || '';
        authorField.value = blog.author || '';

        // Generate slug if it doesn't exist (backward compatibility)
        if (blog.slug) {
          slugField.value = blog.slug;
          slugField.dataset.autoGenerated = 'false';
        } else {
          // Auto-generate slug from title for older blogs
          const generatedSlug = blog.title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          slugField.value = generatedSlug;
          slugField.dataset.autoGenerated = 'true';
        }

        // Content fields
        excerptField.value = blog.excerpt || '';
        contentField.value = blog.content || '';

        // SEO & Metadata fields (with backward compatibility)
        // Use excerpt as fallback for meta description if it doesn't exist
        if (blog.metaDescription !== undefined && blog.metaDescription !== null) {
          metaDescriptionField.value = blog.metaDescription;
        } else {
          // Generate from excerpt or content for older blogs
          const fallbackMeta = blog.excerpt || blog.content.replace(/<[^>]*>/g, '').substring(0, 160);
          metaDescriptionField.value =
            fallbackMeta.length > 160 ? fallbackMeta.substring(0, 160) + '...' : fallbackMeta;
        }

        focusKeywordField.value = blog.focusKeyword || '';

        // Handle tags - ensure proper format
        if (blog.tags !== undefined && blog.tags !== null) {
          if (Array.isArray(blog.tags)) {
            tagsField.value = blog.tags.join(', ');
          } else if (typeof blog.tags === 'string') {
            tagsField.value = blog.tags;
          } else {
            tagsField.value = '';
          }
        } else {
          tagsField.value = '';
        }

        // Publishing Options
        featuredField.checked = blog.featured === true;
        statusField.value = blog.status || 'published';

        // Update character counters after populating fields
        updateCharCounter(excerptField, 200);
        updateCharCounter(metaDescriptionField, 160);

        // Set category (handle both old and new blog formats)
        if (blog.category && Array.isArray(blog.category)) {
          if (blog.category.includes('parent') && blog.category.includes('tutor')) {
            categoryField.value = 'general';
          } else if (blog.category.includes('parent')) {
            categoryField.value = 'parent';
          } else if (blog.category.includes('tutor')) {
            categoryField.value = 'tutor';
          } else {
            categoryField.value = 'general';
          }
        } else {
          // Fallback for older blog format or missing category
          categoryField.value = 'general';
        }

        // Set publish date
        if (blog.publishDate) {
          const date = new Date(blog.publishDate);
          publishDateField.value = date.toISOString().slice(0, 16);
        } else {
          publishDateField.value = '';
        }

        // Show current image if exists
        if (blog.imagePath) {
          currentImagePreview.style.display = 'block';
          currentImagePreview.querySelector('img').src = blog.imagePath;
          removeImageCheckbox.checked = false;
        } else {
          currentImagePreview.style.display = 'none';
          removeImageCheckbox.checked = false;
        }

        // Update UI for edit mode
        formHeading.textContent = 'Edit Blog Post';
        submitBtn.textContent = 'Update Blog';
        cancelEditBtn.style.display = 'inline-block';
        blogForm.dataset.editId = blog._id;

        console.log('Blog form populated for editing:', {
          title: blog.title,
          slug: blog.slug,
          status: blog.status,
          featured: blog.featured,
          tags: blog.tags,
          metaDescription: blog.metaDescription,
        });
      }

      /* Load blogs function */
      async function loadBlogs(category = 'all') {
        const blogListBody = document.getElementById('blogListBody');
        blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading blog posts...</td></tr>';

        try {
          const response = await fetch('/api/blog-writer', {
            credentials: 'include', // Include cookies for authentication
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch blogs: ${response.status} ${response.statusText}`);
          }

          const blogs = await response.json();
          allBlogs = blogs; // Store for editing

          // Filter blogs by category if needed
          let filteredBlogs = blogs;
          if (category !== 'all') {
            if (category === 'general') {
              // General means both parent and tutor categories
              filteredBlogs = blogs.filter(blog => blog.category.includes('parent') && blog.category.includes('tutor'));
            } else {
              // Filter by specific category
              filteredBlogs = blogs.filter(blog => blog.category.includes(category));
            }
          }

          // Clear loading message
          blogListBody.innerHTML = '';

          if (filteredBlogs.length === 0) {
            blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">No blog posts found</td></tr>';
            return;
          }

          // Add each blog to the table
          filteredBlogs.forEach(blog => {
            const row = document.createElement('tr');

            // Format date
            const date = new Date(blog.publishDate || blog.createdAt);
            const formattedDate = date.toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            });

            // Format category
            let categoryText = blog.category.join(', ');
            if (blog.category.includes('parent') && blog.category.includes('tutor')) {
              categoryText = 'General';
            }

            row.innerHTML = `
                        <td>${blog.title}</td>
                        <td>${blog.author}</td>
                        <td>${categoryText}</td>
                        <td>${formattedDate}</td>
                        <td>
                            <button class="action-btn edit-btn" data-id="${blog._id}" title="Edit">✏️</button>
                            <button class="action-btn delete-btn" data-id="${blog._id}" title="Delete">🗑️</button>
                        </td>
                    `;

            blogListBody.appendChild(row);
          });
        } catch (error) {
          console.error('Error loading blogs:', error);
          blogListBody.innerHTML = `<tr><td colspan="5" class="loading-message">Error: ${error.message}</td></tr>`;
        }
      }

      /* Handle edit and delete actions */
      document.getElementById('blogListBody').addEventListener('click', async e => {
        const blogId = e.target.dataset.id;

        if (e.target.classList.contains('edit-btn')) {
          // Find the blog to edit
          const blog = allBlogs.find(b => b._id === blogId);
          if (!blog) {
            alert('Blog not found for editing');
            return;
          }

          console.log('Editing blog:', blog);

          // Set form to edit mode BEFORE switching tabs
          blogForm.dataset.editId = blogId;
          submitBtn.textContent = 'Update Blog Post';

          // Populate form and switch to create tab
          populateBlogForm(blog);
          createTabBtn.click();
          blogForm.scrollIntoView({ behavior: 'smooth' });
        } else if (e.target.classList.contains('delete-btn')) {
          if (confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
            try {
              const response = await fetch(`/api/blog-writer?id=${blogId}`, {
                method: 'DELETE',
                credentials: 'include', // Include cookies for authentication
              });

              if (!response.ok) {
                throw new Error(`Failed to delete blog: ${response.status} ${response.statusText}`);
              }

              alert('Blog post deleted successfully');
              loadBlogs(document.getElementById('blogCategoryFilter').value); // Reload the list
            } catch (error) {
              console.error('Error deleting blog:', error);
              alert(`Error deleting blog: ${error.message}`);
            }
          }
        }
      });

      /* Filter blogs by category */
      document.getElementById('blogCategoryFilter').addEventListener('change', e => {
        loadBlogs(e.target.value);
      });

      /* Refresh blogs button */
      document.getElementById('refreshBlogsBtn').addEventListener('click', () => {
        loadBlogs(document.getElementById('blogCategoryFilter').value);
      });

      /* Migration button */
      document.getElementById('migrateBlogsBtn').addEventListener('click', async () => {
        if (
          !confirm(
            'This will update all existing blog posts to include the new metadata fields. This is safe but irreversible. Continue?'
          )
        ) {
          return;
        }

        const btn = document.getElementById('migrateBlogsBtn');
        const originalText = btn.textContent;
        btn.textContent = 'Updating...';
        btn.disabled = true;

        try {
          const response = await fetch('/api/blog-writer?migrate=true', {
            credentials: 'include',
          });

          if (!response.ok) {
            throw new Error(`Migration failed: ${response.status} ${response.statusText}`);
          }

          const result = await response.json();
          alert(`Migration completed successfully! Updated ${result.updated} blog posts.`);

          // Refresh the blog list to show updated data
          loadBlogs(document.getElementById('blogCategoryFilter').value);
        } catch (error) {
          console.error('Migration error:', error);
          alert('Migration failed: ' + error.message);
        } finally {
          btn.textContent = originalText;
          btn.disabled = false;
        }
      });

      /* Cancel edit button */
      cancelEditBtn.addEventListener('click', () => {
        resetBlogForm();
      });

      /* Enhanced form workflow for creating and editing blogs */
      blogForm.addEventListener('submit', async e => {
        e.preventDefault();

        const isEditing = !!blogForm.dataset.editId;
        const blogId = blogForm.dataset.editId;

        const title = titleField.value.trim();
        const author = authorField.value.trim();
        const slug = slugField.value.trim();
        const category = categoryField.value;
        const status = statusField.value;
        const excerpt = excerptField.value.trim();
        const metaDescription = metaDescriptionField.value.trim();
        const focusKeyword = focusKeywordField.value.trim();
        const tags = tagsField.value.trim();
        const featured = featuredField.checked;
        const publishDate = publishDateField.value;
        const content = contentField.value.trim();

        // Validate required fields
        if (!title || !content || !excerpt) {
          alert('Title, content, and excerpt are required');
          return;
        }

        /* Handle image upload */
        let uploadedImagePath = '';
        if (imageField.files[0]) {
          try {
            uploadedImagePath = await uploadImage(imageField.files[0], 'blog');
          } catch (err) {
            return alert('Upload failed: ' + err.message);
          }
        }

        /* Determine method and URL based on mode */
        const method = isEditing ? 'PUT' : 'POST';
        const url = isEditing ? `/api/blog-writer?id=${blogId}` : '/api/blog-writer';

        /* Build payload */
        const payload = {
          title,
          author: author || 'Tutors Alliance Scotland',
          slug,
          category,
          status,
          excerpt,
          metaDescription,
          focusKeyword,
          tags: tags
            ? tags
                .split(',')
                .map(tag => tag.trim())
                .filter(tag => tag)
            : [],
          featured,
          publishDate,
          content,
          imagePath: uploadedImagePath || '',
          removeImage: removeImageCheckbox.checked,
        };

        // For POST fallback, add editId to payload
        if (isEditing) {
          payload.editId = blogId;
        }

        /* JSON LD for SEO (only for new blogs) */
        if (!isEditing) {
          const nowISO = new Date().toISOString();
          blogMicrodata.textContent = JSON.stringify(
            {
              '@context': 'https://schema.org',
              '@type': 'BlogPosting',
              mainEntityOfPage: { '@id': 'https://tutorsalliancescotland.co.uk/blog', '@type': 'WebPage' },
              headline: title,
              author: { '@type': 'Person', name: author || 'Tutors Alliance Scotland' },
              publisher: {
                '@type': 'Organization',
                name: 'Tutors Alliance Scotland',
                logo: { '@type': 'ImageObject', url: 'https://tutorsalliancescotland.co.uk/images/bannerShield2.png' },
              },
              datePublished: publishDate ? new Date(publishDate).toISOString() : nowISO,
              dateModified: nowISO,
              description: excerpt || content.slice(0, 160) + '…',
              image: uploadedImagePath || 'https://tutorsalliancescotland.co.uk/images/defaultBlog.png',
            },
            null,
            2
          );
        }

        try {
          /* Send to server */
          const response = await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
            credentials: 'include',
          });

          if (!response.ok) {
            throw new Error(await response.text());
          }

          const result = await response.json();
          alert(`Blog ${isEditing ? 'updated' : 'created'} successfully!`);

          resetBlogForm();
          loadBlogs(document.getElementById('blogCategoryFilter').value); // Refresh list
          manageTabBtn.click(); // Switch to manage tab
        } catch (error) {
          console.error('Submit error:', error);
          alert('Error: ' + error.message);
        }
      });
    </script>
  </body>
</html>
