<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">14.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <Name>tutorScotland</Name>
    <RootNamespace>tutorScotland</RootNamespace>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>78bc1803-971f-43f1-8b00-906d5a04f472</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>
    </StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <ProjectTypeGuids>{3AF33F2E-1136-4D97-BBB7-1795711AC8B8};{349c5851-65df-11da-9384-00065b846f21};{9092AA53-FB77-4645-B42D-1CCCA6BD08BD}</ProjectTypeGuids>
    <NodejsPort>1337</NodejsPort>
    <StartWebBrowser>true</StartWebBrowser>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Content Include=".env">
      <SubType>Code</SubType>
    </Content>
    <Content Include=".gitignore">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\addTutor.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\auth-consolidated.js" />
    <Content Include="api\blog-consolidated.js" />
    <Content Include="api\blog-writer.js" />
    <Content Include="api\blog.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\connection.js" />
    <Content Include="api\connectToDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\content-consolidated.js" />
    <Content Include="api\content-manager.js" />
    <Content Include="api\login.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\page.js" />
    <Content Include="api\protected.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\sections.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\tutors-consolidated.js" />
    <Content Include="api\upload-image.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\utilities-consolidated.js" />
    <Content Include="docs\css-technical-debt.md" />
    <Content Include="docs\gmail-nodemailer-troubleshooting.md" />
    <Content Include="docs\gmail-setup-guide.md" />
    <Content Include="models\Blog.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\Order.js" />
    <Content Include="models\Section.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\Tutor.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\User.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="package-lock.json" />
    <Content Include="public\admin.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\blogWriter.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\cache-buster.js" />
    <Content Include="public\contact.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\css\admin-forms.css" />
    <Content Include="public\css\admin-tables.css" />
    <Content Include="public\css\animation-module.css" />
    <Content Include="public\css\button-module.css" />
    <Content Include="public\css\footer-module.css" />
    <Content Include="public\css\layout-module.css" />
    <Content Include="public\css\nav.css" />
    <Content Include="public\css\typography-module.css" />
    <Content Include="public\css\ui-responsive-module.css" />
    <Content Include="public\editor.css" />
    <Content Include="public\header-banner.css" />
    <Content Include="public\index.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\images\bannerBackground.PNG" />
    <Content Include="public\images\bannerShield2.png" />
    <Content Include="public\images\bannerWithRibbons.png" />
    <Content Include="public\images\centralShield.png" />
    <Content Include="public\images\childStudy.PNG" />
    <Content Include="public\images\englishTutor.PNG" />
    <Content Include="public\images\englishTutor2.PNG" />
    <Content Include="public\images\englishTutor3.PNG" />
    <Content Include="public\images\favicon2.png" />
    <Content Include="public\images\flag.PNG" />
    <Content Include="public\images\legoLeft.png" />
    <Content Include="public\images\legoRight.png" />
    <Content Include="public\images\mainMaxim.png" />
    <Content Include="public\images\mathsTutor.PNG" />
    <Content Include="public\images\mathsTutor3.PNG" />
    <Content Include="public\images\mathsTutor4.PNG" />
    <Content Include="public\images\parentAndChild.png" />
    <Content Include="public\images\questionCurl.png" />
    <Content Include="public\images\questionPeriod.png" />
    <Content Include="public\images\subscribe-computer.PNG" />
    <Content Include="public\images\tutor0.jpg" />
    <Content Include="public\images\tutor1.jpg" />
    <Content Include="public\images\tutor2.jpg" />
    <Content Include="public\images\weAreTheFuture.PNG" />
    <Content Include="public\js\dynamic-nav.js" />
    <Content Include="public\js\dynamic-page.js" />
    <Content Include="public\js\dynamic-sections-template.html" />
    <Content Include="public\js\dynamic-sections.js" />
    <Content Include="public\js\editor\api-service.js" />
    <Content Include="public\js\editor\editor-state.js" />
    <Content Include="public\js\editor\features\image-browser.js" />
    <Content Include="public\js\editor\features\section-sorter.js" />
    <Content Include="public\js\editor\override-engine.js" />
    <Content Include="public\js\editor\ui-manager.js" />
    <Content Include="public\js\nav-loader.js" />
    <Content Include="public\js\pages.js" />
    <Content Include="public\js\rolling-banner.js" />
    <Content Include="public\js\upload-helper.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\js\video-player.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\js\visual-editor.js" />
    <Content Include="public\page-template.html" />
    <Content Include="public\page.html" />
    <Content Include="public\parents.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\partials\main-nav.html" />
    <Content Include="public\publicConnect.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="index\config.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\about-us.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\login.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\partnerships.html" />
    <Content Include="public\responsive-helper.js" />
    <Content Include="public\style.css">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\tutors.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\styles2.css">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorConnect.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorDirectory.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorMembership.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorszone.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="seedBlogs.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="seedDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="package.json" />
    <Content Include="README.md" />
    <Content Include="seedUsers.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="tests\config\mocks.js" />
    <Content Include="tests\config\setup.js" />
    <Content Include="tests\config\test-db.js" />
    <Content Include="tests\e2e\dynamic-sections-cross-browser.spec.js" />
    <Content Include="tests\e2e\dynamic-sections-styling.spec.js" />
    <Content Include="tests\e2e\dynamic-sections-visual-regression.spec.js" />
    <Content Include="tests\e2e\hardware-specific.spec.js" />
    <Content Include="tests\fixtures\data\tutors.json" />
    <Content Include="tests\fixtures\data\users.json" />
    <Content Include="tests\fixtures\helpers\auth-helpers.js" />
    <Content Include="tests\integration\api\blog-system.test.js" />
    <Content Include="tests\integration\api\dynamic-sections.test.js" />
    <Content Include="tests\integration\api\image-upload.test.js" />
    <Content Include="tests\integration\api\login.test.js" />
    <Content Include="tests\integration\api\tutor-search.test.js" />
    <Content Include="tests\integration\models\section-schema.test.js" />
    <Content Include="tests\migration\schema-migration.test.js" />
    <Content Include="tests\README.md" />
    <Content Include="tests\setup\test-db.js" />
    <Content Include="tests\smoke\css-preservation.test.js" />
    <Content Include="tests\smoke\css-preservation.test.js-snapshots\dynamic-sections-styling-chromium-win32.png" />
    <Content Include="tests\smoke\css-preservation.test.js-snapshots\team-sections-landscape-chromium-win32.png" />
    <Content Include="tests\smoke\css-preservation.test.js-snapshots\team-sections-landscape-Mobile-Chrome-win32.png" />
    <Content Include="tests\smoke\css-preservation.test.js-snapshots\team-sections-portrait-chromium-win32.png" />
    <Content Include="tests\smoke\css-preservation.test.js-snapshots\team-sections-portrait-Mobile-Chrome-win32.png" />
    <Content Include="tests\smoke\health-check.test.js" />
    <Content Include="tests\smoke\navigation.test.js" />
    <Content Include="tests\smoke\visual-regression.test.js" />
    <Content Include="tests\TESTING_STRATEGY.md" />
    <Content Include="tests\unit\models\User.test.js" />
    <Content Include="updateDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="vercel.json">
      <SubType>Code</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="docs\" />
    <Folder Include="index\" />
    <Folder Include="api\" />
    <Folder Include="models\" />
    <Folder Include="public\" />
    <Folder Include="public\css\" />
    <Folder Include="public\images\" />
    <Folder Include="public\js\" />
    <Folder Include="public\js\editor\" />
    <Folder Include="public\js\editor\features\" />
    <Folder Include="public\partials\" />
    <Folder Include="tests\" />
    <Folder Include="tests\config\" />
    <Folder Include="tests\e2e\" />
    <Folder Include="tests\fixtures\" />
    <Folder Include="tests\fixtures\data\" />
    <Folder Include="tests\fixtures\helpers\" />
    <Folder Include="tests\integration\" />
    <Folder Include="tests\integration\api\" />
    <Folder Include="tests\integration\models\" />
    <Folder Include="tests\migration\" />
    <Folder Include="tests\setup\" />
    <Folder Include="tests\smoke\" />
    <Folder Include="tests\smoke\css-preservation.test.js-snapshots\" />
    <Folder Include="tests\unit\" />
    <Folder Include="tests\unit\models\" />
  </ItemGroup>
  <Import Project="$(VSToolsPath)\Node.js Tools\Microsoft.NodejsToolsV2.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:48022/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>http://localhost:1337</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}" User="">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>False</EnableENC>
          <AlwaysStartWebServerOnDebug>False</AlwaysStartWebServerOnDebug>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>